-- ملف تحديث قاعدة البيانات للمشاريع الموجودة
-- يجب تشغيل هذا الملف إذا كانت قاعدة البيانات موجودة مسبقاً

USE employee_management;

-- تحدي<PERSON> جدول الأقسام
ALTER TABLE departments 
ADD COLUMN manager_name VARCHAR(100) AFTER description;

-- تحديث جدول المناصب
ALTER TABLE positions 
ADD COLUMN min_salary DECIMAL(10,2) AFTER description,
ADD COLUMN max_salary DECIMAL(10,2) AFTER min_salary,
ADD COLUMN requirements TEXT AFTER max_salary,
DROP COLUMN salary_range;

-- تحديث جدول الموظفين
ALTER TABLE employees 
ADD COLUMN gender ENUM('male', 'female') NOT NULL AFTER birth_date,
CHANGE COLUMN education_level qualification ENUM('ثانوية', 'دبلوم متوسط', 'دبلوم عالي', 'بكالوريوس', 'ليسانس', 'ماجستير', 'دكتوراة');

-- تحديث جدول المستخدمين
ALTER TABLE users 
ADD COLUMN employee_id INT UNIQUE AFTER role,
MODIFY COLUMN status ENUM('active', 'inactive', 'suspended') DEFAULT 'active';

-- تحديث جدول أنواع الإجازات
ALTER TABLE leave_types 
CHANGE COLUMN days_per_year max_days_per_year INT,
ADD COLUMN carry_forward BOOLEAN DEFAULT FALSE AFTER is_paid,
ADD COLUMN gender_specific ENUM('male', 'female') NULL AFTER carry_forward,
ADD COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;

-- حذف وإعادة إنشاء جدول إعدادات الشركة
DROP TABLE IF EXISTS company_settings;

CREATE TABLE company_settings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    company_name VARCHAR(255) NOT NULL,
    company_name_en VARCHAR(255),
    address TEXT,
    phone VARCHAR(50),
    email VARCHAR(100),
    website VARCHAR(255),
    tax_number VARCHAR(50),
    commercial_register VARCHAR(50),
    logo VARCHAR(500),
    work_start_time TIME DEFAULT '08:00:00',
    work_end_time TIME DEFAULT '17:00:00',
    weekend_days VARCHAR(100) DEFAULT 'friday,saturday',
    currency VARCHAR(10) DEFAULT 'SAR',
    timezone VARCHAR(50) DEFAULT 'Asia/Riyadh',
    language VARCHAR(10) DEFAULT 'ar',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- إدراج إعدادات الشركة الافتراضية
INSERT INTO company_settings (
    company_name, company_name_en, address, phone, email, website,
    work_start_time, work_end_time, weekend_days, currency, timezone, language
) VALUES (
    'شركة المثال للتجارة', 'Example Trading Company', 
    'الرياض، المملكة العربية السعودية', '+966-11-1234567', 
    '<EMAIL>', 'www.example.com',
    '08:00:00', '17:00:00', 'friday,saturday', 'SAR', 'Asia/Riyadh', 'ar'
);

-- إضافة المفاتيح الخارجية
ALTER TABLE users ADD FOREIGN KEY (employee_id) REFERENCES employees(id) ON DELETE SET NULL;

-- تحديث أنواع الإجازات الموجودة
UPDATE leave_types SET 
    carry_forward = TRUE, 
    gender_specific = NULL 
WHERE name = 'إجازة سنوية';

UPDATE leave_types SET 
    carry_forward = FALSE, 
    gender_specific = NULL 
WHERE name = 'إجازة مرضية';

UPDATE leave_types SET 
    carry_forward = FALSE, 
    gender_specific = NULL 
WHERE name = 'إجازة اضطرارية';

UPDATE leave_types SET 
    carry_forward = FALSE, 
    gender_specific = 'female' 
WHERE name = 'إجازة أمومة';

UPDATE leave_types SET 
    carry_forward = FALSE, 
    gender_specific = 'male' 
WHERE name = 'إجازة أبوة';

UPDATE leave_types SET 
    carry_forward = FALSE, 
    gender_specific = NULL 
WHERE name = 'إجازة حج';

-- إضافة أنواع إجازات جديدة إذا لم تكن موجودة
INSERT IGNORE INTO leave_types (name, description, max_days_per_year, max_consecutive_days, is_paid, carry_forward, gender_specific) VALUES
('إجازة سنوية', 'الإجازة السنوية العادية', 30, 15, TRUE, TRUE, NULL),
('إجازة مرضية', 'إجازة للحالات المرضية', 15, 7, TRUE, FALSE, NULL),
('إجازة اضطرارية', 'إجازة للظروف الطارئة', 5, 3, FALSE, FALSE, NULL),
('إجازة أمومة', 'إجازة الأمومة للموظفات', 90, 90, TRUE, FALSE, 'female'),
('إجازة أبوة', 'إجازة الأبوة للموظفين', 3, 3, TRUE, FALSE, 'male'),
('إجازة حج', 'إجازة لأداء فريضة الحج', 15, 15, TRUE, FALSE, NULL);

-- إضافة أقسام افتراضية إذا لم تكن موجودة
INSERT IGNORE INTO departments (name, description) VALUES
('الموارد البشرية', 'قسم إدارة الموارد البشرية والشؤون الإدارية'),
('المالية والمحاسبة', 'قسم الشؤون المالية والمحاسبية'),
('تقنية المعلومات', 'قسم تقنية المعلومات والدعم التقني'),
('التسويق والمبيعات', 'قسم التسويق والمبيعات'),
('العمليات', 'قسم العمليات والإنتاج'),
('خدمة العملاء', 'قسم خدمة العملاء والدعم');

-- إنشاء المستخدم الافتراضي إذا لم يكن موجوداً
INSERT IGNORE INTO users (username, email, password, role) VALUES
('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'admin');

COMMIT;
