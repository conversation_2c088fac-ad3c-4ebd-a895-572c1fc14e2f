<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// التحقق من تسجيل الدخول والصلاحيات
if (!isset($_SESSION['user_id']) || !in_array($_SESSION['role'], ['admin', 'hr'])) {
    header('Location: ../login.php');
    exit();
}

$page_title = 'إدارة الأقسام';
$success_message = '';
$error_message = '';

try {
    $pdo = getConnection();
    
    // الحصول على قائمة الأقسام
    $departments_stmt = $pdo->query("
        SELECT 
            d.*,
            COUNT(e.id) as employee_count
        FROM departments d
        LEFT JOIN employees e ON d.id = e.department_id AND e.status = 'active'
        GROUP BY d.id
        ORDER BY d.name
    ");
    $departments = $departments_stmt->fetchAll();
    
} catch (PDOException $e) {
    $departments = [];
}

// معالجة إضافة/تعديل قسم
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    try {
        $pdo = getConnection();
        
        $action = $_POST['action'];
        $name = sanitizeInput($_POST['name']);
        $description = sanitizeInput($_POST['description']);
        $manager_name = sanitizeInput($_POST['manager_name']);
        $status = $_POST['status'];
        
        if (empty($name)) {
            throw new Exception('يرجى إدخال اسم القسم');
        }
        
        if ($action == 'add') {
            // التحقق من عدم تكرار الاسم
            $check_stmt = $pdo->prepare("SELECT COUNT(*) FROM departments WHERE name = ?");
            $check_stmt->execute([$name]);
            if ($check_stmt->fetchColumn() > 0) {
                throw new Exception('اسم القسم موجود مسبقاً');
            }
            
            // إضافة قسم جديد
            $insert_stmt = $pdo->prepare("
                INSERT INTO departments (name, description, manager_name, status, created_at)
                VALUES (?, ?, ?, ?, NOW())
            ");
            $insert_stmt->execute([$name, $description, $manager_name, $status]);
            
            logActivity($_SESSION['user_id'], 'إضافة قسم', "تم إضافة القسم: $name");
            $success_message = 'تم إضافة القسم بنجاح';
            
        } elseif ($action == 'edit') {
            $department_id = (int)$_POST['department_id'];
            
            // التحقق من عدم تكرار الاسم (باستثناء القسم الحالي)
            $check_stmt = $pdo->prepare("SELECT COUNT(*) FROM departments WHERE name = ? AND id != ?");
            $check_stmt->execute([$name, $department_id]);
            if ($check_stmt->fetchColumn() > 0) {
                throw new Exception('اسم القسم موجود مسبقاً');
            }
            
            // تحديث القسم
            $update_stmt = $pdo->prepare("
                UPDATE departments 
                SET name = ?, description = ?, manager_name = ?, status = ?, updated_at = NOW()
                WHERE id = ?
            ");
            $update_stmt->execute([$name, $description, $manager_name, $status, $department_id]);
            
            logActivity($_SESSION['user_id'], 'تعديل قسم', "تم تعديل القسم: $name");
            $success_message = 'تم تحديث القسم بنجاح';
        }
        
        // إعادة تحميل الأقسام
        $departments_stmt = $pdo->query("
            SELECT 
                d.*,
                COUNT(e.id) as employee_count
            FROM departments d
            LEFT JOIN employees e ON d.id = e.department_id AND e.status = 'active'
            GROUP BY d.id
            ORDER BY d.name
        ");
        $departments = $departments_stmt->fetchAll();
        
    } catch (Exception $e) {
        $error_message = $e->getMessage();
    }
}

// معالجة حذف قسم
if (isset($_GET['delete']) && is_numeric($_GET['delete'])) {
    try {
        $pdo = getConnection();
        $department_id = (int)$_GET['delete'];
        
        // التحقق من وجود موظفين في القسم
        $check_stmt = $pdo->prepare("SELECT COUNT(*) FROM employees WHERE department_id = ?");
        $check_stmt->execute([$department_id]);
        $employee_count = $check_stmt->fetchColumn();
        
        if ($employee_count > 0) {
            throw new Exception('لا يمكن حذف القسم لوجود موظفين مرتبطين به');
        }
        
        // حذف القسم
        $delete_stmt = $pdo->prepare("DELETE FROM departments WHERE id = ?");
        $delete_stmt->execute([$department_id]);
        
        logActivity($_SESSION['user_id'], 'حذف قسم', "تم حذف قسم برقم: $department_id");
        $success_message = 'تم حذف القسم بنجاح';
        
        // إعادة توجيه لتجنب إعادة الحذف
        header('Location: departments.php?success=' . urlencode($success_message));
        exit();
        
    } catch (Exception $e) {
        $error_message = $e->getMessage();
    }
}

// رسالة النجاح من إعادة التوجيه
if (isset($_GET['success'])) {
    $success_message = $_GET['success'];
}

include '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
            <div class="position-sticky pt-3">
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link" href="../index.php">
                            <i class="fas fa-tachometer-alt me-2"></i>
                            لوحة التحكم
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="company.php">
                            <i class="fas fa-building me-2"></i>
                            إعدادات الشركة
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="departments.php">
                            <i class="fas fa-sitemap me-2"></i>
                            إدارة الأقسام
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="positions.php">
                            <i class="fas fa-user-tie me-2"></i>
                            المسميات الوظيفية
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="leave_types.php">
                            <i class="fas fa-calendar-alt me-2"></i>
                            أنواع الإجازات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="users.php">
                            <i class="fas fa-users-cog me-2"></i>
                            إدارة المستخدمين
                        </a>
                    </li>
                </ul>
            </div>
        </nav>
        
        <!-- Main Content -->
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">
                    <i class="fas fa-sitemap me-2"></i>
                    إدارة الأقسام
                </h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#departmentModal" onclick="openAddModal()">
                        <i class="fas fa-plus me-1"></i>
                        إضافة قسم جديد
                    </button>
                </div>
            </div>

            <?php if ($success_message): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle me-2"></i>
                    <?php echo $success_message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?php if ($error_message): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <?php echo $error_message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <!-- قائمة الأقسام -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-list me-2"></i>
                        قائمة الأقسام (<?php echo count($departments); ?>)
                    </h6>
                </div>
                <div class="card-body">
                    <?php if (empty($departments)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-sitemap fa-3x text-muted mb-3"></i>
                            <h5>لا توجد أقسام</h5>
                            <p class="text-muted">ابدأ بإضافة أول قسم في الشركة</p>
                            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#departmentModal" onclick="openAddModal()">
                                <i class="fas fa-plus me-1"></i>
                                إضافة قسم جديد
                            </button>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-bordered data-table" id="departmentsTable" width="100%" cellspacing="0">
                                <thead>
                                    <tr>
                                        <th>اسم القسم</th>
                                        <th>الوصف</th>
                                        <th>مدير القسم</th>
                                        <th>عدد الموظفين</th>
                                        <th>الحالة</th>
                                        <th>تاريخ الإنشاء</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($departments as $dept): ?>
                                    <tr>
                                        <td>
                                            <strong><?php echo htmlspecialchars($dept['name']); ?></strong>
                                        </td>
                                        <td>
                                            <?php if ($dept['description']): ?>
                                                <span title="<?php echo htmlspecialchars($dept['description']); ?>">
                                                    <?php echo htmlspecialchars(substr($dept['description'], 0, 50)) . (strlen($dept['description']) > 50 ? '...' : ''); ?>
                                                </span>
                                            <?php else: ?>
                                                <span class="text-muted">لا يوجد وصف</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if ($dept['manager_name']): ?>
                                                <i class="fas fa-user-tie me-1"></i>
                                                <?php echo htmlspecialchars($dept['manager_name']); ?>
                                            <?php else: ?>
                                                <span class="text-muted">غير محدد</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <span class="badge bg-info">
                                                <?php echo $dept['employee_count']; ?> موظف
                                            </span>
                                        </td>
                                        <td>
                                            <?php
                                            $status_classes = [
                                                'active' => 'success',
                                                'inactive' => 'secondary'
                                            ];
                                            $status_text = [
                                                'active' => 'نشط',
                                                'inactive' => 'غير نشط'
                                            ];
                                            $status_class = $status_classes[$dept['status']] ?? 'secondary';
                                            ?>
                                            <span class="badge bg-<?php echo $status_class; ?>">
                                                <?php echo $status_text[$dept['status']] ?? $dept['status']; ?>
                                            </span>
                                        </td>
                                        <td><?php echo formatDate($dept['created_at']); ?></td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <button type="button" class="btn btn-sm btn-outline-primary" 
                                                        onclick="openEditModal(<?php echo htmlspecialchars(json_encode($dept)); ?>)">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <?php if ($dept['employee_count'] == 0): ?>
                                                <button type="button" class="btn btn-sm btn-outline-danger" 
                                                        onclick="confirmDelete(<?php echo $dept['id']; ?>, '<?php echo htmlspecialchars($dept['name']); ?>')">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                                <?php else: ?>
                                                <button type="button" class="btn btn-sm btn-outline-secondary" disabled 
                                                        title="لا يمكن حذف القسم لوجود موظفين">
                                                    <i class="fas fa-lock"></i>
                                                </button>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </main>
    </div>
</div>

<!-- Modal إضافة/تعديل قسم -->
<div class="modal fade" id="departmentModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="departmentModalTitle">إضافة قسم جديد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" id="departmentForm" class="needs-validation" novalidate>
                <div class="modal-body">
                    <input type="hidden" name="action" id="modal_action" value="add">
                    <input type="hidden" name="department_id" id="modal_department_id">
                    
                    <div class="mb-3">
                        <label for="modal_name" class="form-label">اسم القسم <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="modal_name" name="name" required>
                        <div class="invalid-feedback">يرجى إدخال اسم القسم</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="modal_description" class="form-label">وصف القسم</label>
                        <textarea class="form-control" id="modal_description" name="description" rows="3" 
                                  placeholder="وصف مختصر عن القسم ومهامه..."></textarea>
                    </div>
                    
                    <div class="mb-3">
                        <label for="modal_manager_name" class="form-label">مدير القسم</label>
                        <input type="text" class="form-control" id="modal_manager_name" name="manager_name" 
                               placeholder="اسم مدير القسم">
                    </div>
                    
                    <div class="mb-3">
                        <label for="modal_status" class="form-label">الحالة</label>
                        <select class="form-select" id="modal_status" name="status">
                            <option value="active">نشط</option>
                            <option value="inactive">غير نشط</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary" id="modal_submit_btn">
                        <i class="fas fa-save me-1"></i>
                        حفظ
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// فتح modal للإضافة
function openAddModal() {
    document.getElementById('departmentModalTitle').textContent = 'إضافة قسم جديد';
    document.getElementById('modal_action').value = 'add';
    document.getElementById('modal_submit_btn').innerHTML = '<i class="fas fa-save me-1"></i>إضافة';
    
    // إعادة تعيين النموذج
    document.getElementById('departmentForm').reset();
    document.getElementById('departmentForm').classList.remove('was-validated');
}

// فتح modal للتعديل
function openEditModal(department) {
    document.getElementById('departmentModalTitle').textContent = 'تعديل القسم';
    document.getElementById('modal_action').value = 'edit';
    document.getElementById('modal_submit_btn').innerHTML = '<i class="fas fa-save me-1"></i>تحديث';
    
    // ملء البيانات
    document.getElementById('modal_department_id').value = department.id;
    document.getElementById('modal_name').value = department.name;
    document.getElementById('modal_description').value = department.description || '';
    document.getElementById('modal_manager_name').value = department.manager_name || '';
    document.getElementById('modal_status').value = department.status;
    
    // إزالة validation classes
    document.getElementById('departmentForm').classList.remove('was-validated');
    
    // عرض المودال
    new bootstrap.Modal(document.getElementById('departmentModal')).show();
}

// تأكيد الحذف
function confirmDelete(departmentId, departmentName) {
    Swal.fire({
        title: 'تأكيد الحذف',
        text: `هل تريد حذف القسم: ${departmentName}؟`,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#6c757d',
        confirmButtonText: 'نعم، احذف',
        cancelButtonText: 'إلغاء'
    }).then((result) => {
        if (result.isConfirmed) {
            window.location.href = `departments.php?delete=${departmentId}`;
        }
    });
}

// تفعيل التحقق من صحة النموذج
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();

// تهيئة DataTable
$(document).ready(function() {
    $('#departmentsTable').DataTable({
        language: {
            url: 'https://cdn.datatables.net/plug-ins/1.13.6/i18n/ar.json'
        },
        responsive: true,
        pageLength: 25,
        order: [[0, 'asc']],
        columnDefs: [
            { orderable: false, targets: [6] } // عمود الإجراءات غير قابل للترتيب
        ]
    });
});
</script>

<?php include '../includes/footer.php'; ?>
