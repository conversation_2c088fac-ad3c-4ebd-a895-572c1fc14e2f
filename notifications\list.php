<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit();
}

$page_title = 'الإشعارات والتنبيهات';

try {
    $pdo = getConnection();
    
    // تحديد الإشعارات كمقروءة عند زيارة الصفحة
    $mark_read_stmt = $pdo->prepare("
        UPDATE notifications 
        SET is_read = 1, read_at = NOW() 
        WHERE user_id = ? AND is_read = 0
    ");
    $mark_read_stmt->execute([$_SESSION['user_id']]);
    
    // الحصول على جميع الإشعارات للمستخدم الحالي
    $notifications_stmt = $pdo->prepare("
        SELECT * FROM notifications 
        WHERE user_id = ? 
        ORDE<PERSON> BY created_at DESC 
        LIMIT 100
    ");
    $notifications_stmt->execute([$_SESSION['user_id']]);
    $notifications = $notifications_stmt->fetchAll();
    
    // تجميع الإشعارات حسب التاريخ
    $grouped_notifications = [];
    foreach ($notifications as $notification) {
        $date = date('Y-m-d', strtotime($notification['created_at']));
        $grouped_notifications[$date][] = $notification;
    }
    
    // الحصول على الإشعارات التلقائية (انتهاء العقود، الإقامات، إلخ)
    if (in_array($_SESSION['role'], ['manager', 'hr', 'admin'])) {
        // التحقق من العقود المنتهية قريباً
        $contracts_stmt = $pdo->query("
            SELECT 
                e.first_name, e.last_name, e.contract_end_date,
                DATEDIFF(e.contract_end_date, CURDATE()) as days_remaining
            FROM employees e
            WHERE e.status = 'active' 
            AND e.contract_end_date IS NOT NULL
            AND e.contract_end_date BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 30 DAY)
            ORDER BY e.contract_end_date ASC
        ");
        $expiring_contracts = $contracts_stmt->fetchAll();
        
        // التحقق من الإقامات المنتهية قريباً
        $residences_stmt = $pdo->query("
            SELECT 
                e.first_name, e.last_name, e.residence_expiry,
                DATEDIFF(e.residence_expiry, CURDATE()) as days_remaining
            FROM employees e
            WHERE e.status = 'active' 
            AND e.residence_expiry IS NOT NULL
            AND e.residence_expiry BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 30 DAY)
            ORDER BY e.residence_expiry ASC
        ");
        $expiring_residences = $residences_stmt->fetchAll();
        
        // التحقق من الإجازات المنتهية قريباً
        $leaves_stmt = $pdo->query("
            SELECT 
                lr.*, e.first_name, e.last_name, lt.name as leave_type_name,
                DATEDIFF(lr.end_date, CURDATE()) as days_remaining
            FROM leave_requests lr
            JOIN employees e ON lr.employee_id = e.id
            JOIN leave_types lt ON lr.leave_type_id = lt.id
            WHERE lr.status = 'approved' 
            AND lr.end_date BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 7 DAY)
            ORDER BY lr.end_date ASC
        ");
        $ending_leaves = $leaves_stmt->fetchAll();
    }
    
} catch (PDOException $e) {
    $notifications = [];
    $grouped_notifications = [];
    $expiring_contracts = [];
    $expiring_residences = [];
    $ending_leaves = [];
}

include '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
            <div class="position-sticky pt-3">
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link" href="../index.php">
                            <i class="fas fa-tachometer-alt me-2"></i>
                            لوحة التحكم
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../employees/list.php">
                            <i class="fas fa-users me-2"></i>
                            قائمة الموظفين
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="list.php">
                            <i class="fas fa-bell me-2"></i>
                            الإشعارات
                        </a>
                    </li>
                    <?php if (in_array($_SESSION['role'], ['manager', 'hr', 'admin'])): ?>
                    <li class="nav-item">
                        <a class="nav-link" href="alerts.php">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            التنبيهات التلقائية
                        </a>
                    </li>
                    <?php endif; ?>
                </ul>
            </div>
        </nav>
        
        <!-- Main Content -->
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">
                    <i class="fas fa-bell me-2"></i>
                    الإشعارات والتنبيهات
                </h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <button type="button" class="btn btn-outline-secondary" onclick="markAllAsRead()">
                        <i class="fas fa-check-double me-1"></i>
                        تحديد الكل كمقروء
                    </button>
                </div>
            </div>

            <?php if (in_array($_SESSION['role'], ['manager', 'hr', 'admin'])): ?>
            <!-- التنبيهات التلقائية -->
            <div class="row mb-4">
                <!-- العقود المنتهية قريباً -->
                <?php if (!empty($expiring_contracts)): ?>
                <div class="col-lg-4 mb-3">
                    <div class="card border-warning">
                        <div class="card-header bg-warning text-dark">
                            <h6 class="m-0">
                                <i class="fas fa-file-contract me-2"></i>
                                عقود تنتهي قريباً
                                <span class="badge bg-dark"><?php echo count($expiring_contracts); ?></span>
                            </h6>
                        </div>
                        <div class="card-body p-0">
                            <div class="list-group list-group-flush">
                                <?php foreach (array_slice($expiring_contracts, 0, 5) as $contract): ?>
                                <div class="list-group-item">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <strong><?php echo htmlspecialchars($contract['first_name'] . ' ' . $contract['last_name']); ?></strong>
                                            <br><small class="text-muted"><?php echo formatDate($contract['contract_end_date']); ?></small>
                                        </div>
                                        <span class="badge bg-warning">
                                            <?php echo $contract['days_remaining']; ?> يوم
                                        </span>
                                    </div>
                                </div>
                                <?php endforeach; ?>
                            </div>
                            <?php if (count($expiring_contracts) > 5): ?>
                            <div class="card-footer text-center">
                                <a href="alerts.php#contracts" class="text-warning">
                                    عرض جميع العقود (<?php echo count($expiring_contracts); ?>)
                                </a>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                <?php endif; ?>

                <!-- الإقامات المنتهية قريباً -->
                <?php if (!empty($expiring_residences)): ?>
                <div class="col-lg-4 mb-3">
                    <div class="card border-danger">
                        <div class="card-header bg-danger text-white">
                            <h6 class="m-0">
                                <i class="fas fa-id-card me-2"></i>
                                إقامات تنتهي قريباً
                                <span class="badge bg-light text-dark"><?php echo count($expiring_residences); ?></span>
                            </h6>
                        </div>
                        <div class="card-body p-0">
                            <div class="list-group list-group-flush">
                                <?php foreach (array_slice($expiring_residences, 0, 5) as $residence): ?>
                                <div class="list-group-item">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <strong><?php echo htmlspecialchars($residence['first_name'] . ' ' . $residence['last_name']); ?></strong>
                                            <br><small class="text-muted"><?php echo formatDate($residence['residence_expiry']); ?></small>
                                        </div>
                                        <span class="badge bg-danger">
                                            <?php echo $residence['days_remaining']; ?> يوم
                                        </span>
                                    </div>
                                </div>
                                <?php endforeach; ?>
                            </div>
                            <?php if (count($expiring_residences) > 5): ?>
                            <div class="card-footer text-center">
                                <a href="alerts.php#residences" class="text-danger">
                                    عرض جميع الإقامات (<?php echo count($expiring_residences); ?>)
                                </a>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                <?php endif; ?>

                <!-- الإجازات المنتهية قريباً -->
                <?php if (!empty($ending_leaves)): ?>
                <div class="col-lg-4 mb-3">
                    <div class="card border-info">
                        <div class="card-header bg-info text-white">
                            <h6 class="m-0">
                                <i class="fas fa-calendar-times me-2"></i>
                                إجازات تنتهي قريباً
                                <span class="badge bg-light text-dark"><?php echo count($ending_leaves); ?></span>
                            </h6>
                        </div>
                        <div class="card-body p-0">
                            <div class="list-group list-group-flush">
                                <?php foreach (array_slice($ending_leaves, 0, 5) as $leave): ?>
                                <div class="list-group-item">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <strong><?php echo htmlspecialchars($leave['first_name'] . ' ' . $leave['last_name']); ?></strong>
                                            <br><small class="text-muted"><?php echo htmlspecialchars($leave['leave_type_name']); ?></small>
                                        </div>
                                        <span class="badge bg-info">
                                            <?php echo $leave['days_remaining']; ?> يوم
                                        </span>
                                    </div>
                                </div>
                                <?php endforeach; ?>
                            </div>
                            <?php if (count($ending_leaves) > 5): ?>
                            <div class="card-footer text-center">
                                <a href="alerts.php#leaves" class="text-info">
                                    عرض جميع الإجازات (<?php echo count($ending_leaves); ?>)
                                </a>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
            </div>
            <?php endif; ?>

            <!-- قائمة الإشعارات -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-list me-2"></i>
                        جميع الإشعارات
                    </h6>
                </div>
                <div class="card-body">
                    <?php if (empty($notifications)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-bell-slash fa-3x text-muted mb-3"></i>
                            <h5>لا توجد إشعارات</h5>
                            <p class="text-muted">لم تتلق أي إشعارات بعد</p>
                        </div>
                    <?php else: ?>
                        <div class="timeline">
                            <?php foreach ($grouped_notifications as $date => $date_notifications): ?>
                            <div class="timeline-date">
                                <h6 class="text-primary">
                                    <i class="fas fa-calendar me-2"></i>
                                    <?php echo formatDate($date); ?>
                                </h6>
                            </div>
                            
                            <?php foreach ($date_notifications as $notification): ?>
                            <div class="timeline-item <?php echo $notification['is_read'] ? 'read' : 'unread'; ?>">
                                <div class="timeline-marker">
                                    <?php
                                    $icon_classes = [
                                        'info' => 'fas fa-info-circle text-info',
                                        'success' => 'fas fa-check-circle text-success',
                                        'warning' => 'fas fa-exclamation-triangle text-warning',
                                        'error' => 'fas fa-times-circle text-danger'
                                    ];
                                    $icon_class = $icon_classes[$notification['type']] ?? 'fas fa-bell text-primary';
                                    ?>
                                    <i class="<?php echo $icon_class; ?>"></i>
                                </div>
                                <div class="timeline-content">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div class="flex-grow-1">
                                            <h6 class="mb-1"><?php echo htmlspecialchars($notification['title']); ?></h6>
                                            <p class="mb-1 text-muted"><?php echo htmlspecialchars($notification['message']); ?></p>
                                            <small class="text-muted">
                                                <i class="fas fa-clock me-1"></i>
                                                <?php echo formatDateTime($notification['created_at']); ?>
                                            </small>
                                        </div>
                                        <?php if (!$notification['is_read']): ?>
                                        <span class="badge bg-primary">جديد</span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                            <?php endforeach; ?>
                            <?php endforeach; ?>
                        </div>
                        
                        <?php if (count($notifications) >= 100): ?>
                        <div class="text-center mt-3">
                            <p class="text-muted">يتم عرض آخر 100 إشعار فقط</p>
                        </div>
                        <?php endif; ?>
                    <?php endif; ?>
                </div>
            </div>
        </main>
    </div>
</div>

<style>
.timeline {
    position: relative;
    padding: 20px 0;
}

.timeline-date {
    margin: 30px 0 20px 0;
    padding-bottom: 10px;
    border-bottom: 2px solid #e3e6f0;
}

.timeline-date:first-child {
    margin-top: 0;
}

.timeline-item {
    position: relative;
    padding: 15px 0 15px 60px;
    border-left: 2px solid #e3e6f0;
}

.timeline-item:last-child {
    border-left: none;
}

.timeline-marker {
    position: absolute;
    left: -12px;
    top: 20px;
    width: 24px;
    height: 24px;
    background: white;
    border: 2px solid #e3e6f0;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.timeline-item.unread .timeline-marker {
    border-color: #4e73df;
    background: #4e73df;
    color: white;
}

.timeline-item.unread .timeline-content {
    background: #f8f9fc;
    padding: 15px;
    border-radius: 8px;
    border-left: 4px solid #4e73df;
}

.timeline-content h6 {
    color: #5a5c69;
    font-weight: 600;
}

.timeline-item.unread .timeline-content h6 {
    color: #2e59d9;
}
</style>

<script>
// تحديد جميع الإشعارات كمقروءة
function markAllAsRead() {
    fetch('mark_notifications_read.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // إزالة علامات "غير مقروء"
            document.querySelectorAll('.timeline-item.unread').forEach(item => {
                item.classList.remove('unread');
                item.classList.add('read');
                
                // إزالة badge "جديد"
                const badge = item.querySelector('.badge.bg-primary');
                if (badge) {
                    badge.remove();
                }
            });
            
            showSuccess('تم تحديد جميع الإشعارات كمقروءة');
        } else {
            showError('حدث خطأ أثناء تحديث الإشعارات');
        }
    })
    .catch(error => {
        showError('حدث خطأ أثناء تحديث الإشعارات');
    });
}

// تحديث عداد الإشعارات في الشريط العلوي
document.addEventListener('DOMContentLoaded', function() {
    const notificationBadge = document.querySelector('.notification-badge');
    if (notificationBadge) {
        notificationBadge.style.display = 'none';
    }
});
</script>

<?php include '../includes/footer.php'; ?>
