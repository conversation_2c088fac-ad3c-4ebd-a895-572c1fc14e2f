<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit();
}

$page_title = 'تقارير الموظفين';

// معالجة فلاتر البحث
$department_id = $_GET['department_id'] ?? '';
$position_id = $_GET['position_id'] ?? '';
$status = $_GET['status'] ?? '';
$gender = $_GET['gender'] ?? '';
$qualification = $_GET['qualification'] ?? '';

try {
    $pdo = getConnection();
    
    // الحصول على قائمة الأقسام
    $departments_stmt = $pdo->query("
        SELECT id, name FROM departments 
        WHERE status = 'active' 
        ORDER BY name
    ");
    $departments = $departments_stmt->fetchAll();
    
    // الحصول على قائمة المناصب
    $positions_stmt = $pdo->query("
        SELECT id, title FROM positions 
        WHERE status = 'active' 
        ORDER BY title
    ");
    $positions = $positions_stmt->fetchAll();
    
    // بناء استعلام التقرير
    $where_conditions = ["1=1"];
    $params = [];
    
    if ($department_id) {
        $where_conditions[] = "e.department_id = ?";
        $params[] = $department_id;
    }
    
    if ($position_id) {
        $where_conditions[] = "e.position_id = ?";
        $params[] = $position_id;
    }
    
    if ($status) {
        $where_conditions[] = "e.status = ?";
        $params[] = $status;
    }
    
    if ($gender) {
        $where_conditions[] = "e.gender = ?";
        $params[] = $gender;
    }
    
    if ($qualification) {
        $where_conditions[] = "e.qualification = ?";
        $params[] = $qualification;
    }
    
    $where_clause = implode(' AND ', $where_conditions);
    
    // الحصول على بيانات الموظفين
    $employees_stmt = $pdo->prepare("
        SELECT 
            e.*,
            d.name as department_name,
            p.title as position_title,
            TIMESTAMPDIFF(YEAR, e.birth_date, CURDATE()) as age,
            TIMESTAMPDIFF(YEAR, e.hire_date, CURDATE()) as years_of_service,
            DATEDIFF(e.contract_end_date, CURDATE()) as contract_days_remaining
        FROM employees e
        LEFT JOIN departments d ON e.department_id = d.id
        LEFT JOIN positions p ON e.position_id = p.id
        WHERE $where_clause
        ORDER BY e.first_name, e.last_name
    ");
    $employees_stmt->execute($params);
    $employees = $employees_stmt->fetchAll();
    
    // حساب الإحصائيات
    $total_employees = count($employees);
    $active_employees = count(array_filter($employees, function($e) { return $e['status'] == 'active'; }));
    $male_employees = count(array_filter($employees, function($e) { return $e['gender'] == 'male'; }));
    $female_employees = count(array_filter($employees, function($e) { return $e['gender'] == 'female'; }));
    
    // إحصائيات المؤهلات
    $qualifications_stats = [];
    foreach ($employees as $emp) {
        $qual = $emp['qualification'] ?: 'غير محدد';
        $qualifications_stats[$qual] = ($qualifications_stats[$qual] ?? 0) + 1;
    }
    
    // إحصائيات الأعمار
    $age_groups = [
        '20-30' => 0,
        '31-40' => 0,
        '41-50' => 0,
        '51-60' => 0,
        '60+' => 0
    ];
    
    foreach ($employees as $emp) {
        $age = $emp['age'];
        if ($age <= 30) $age_groups['20-30']++;
        elseif ($age <= 40) $age_groups['31-40']++;
        elseif ($age <= 50) $age_groups['41-50']++;
        elseif ($age <= 60) $age_groups['51-60']++;
        else $age_groups['60+']++;
    }
    
} catch (PDOException $e) {
    $employees = [];
    $departments = [];
    $positions = [];
}

include '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
            <div class="position-sticky pt-3">
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link" href="../index.php">
                            <i class="fas fa-tachometer-alt me-2"></i>
                            لوحة التحكم
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">
                            <i class="fas fa-chart-pie me-2"></i>
                            تقارير النظام
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="employees.php">
                            <i class="fas fa-users me-2"></i>
                            تقارير الموظفين
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../attendance/reports.php">
                            <i class="fas fa-clock me-2"></i>
                            تقارير الحضور
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="leaves.php">
                            <i class="fas fa-calendar-alt me-2"></i>
                            تقارير الإجازات
                        </a>
                    </li>
                </ul>
            </div>
        </nav>
        
        <!-- Main Content -->
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">
                    <i class="fas fa-users me-2"></i>
                    تقارير الموظفين
                </h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <button type="button" class="btn btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
                            <i class="fas fa-download me-1"></i>
                            تصدير التقرير
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" onclick="exportEmployees('excel')">
                                <i class="fas fa-file-excel me-2"></i>Excel
                            </a></li>
                            <li><a class="dropdown-item" href="#" onclick="exportEmployees('pdf')">
                                <i class="fas fa-file-pdf me-2"></i>PDF
                            </a></li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- فلاتر التقرير -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-filter me-2"></i>
                        فلاتر التقرير
                    </h6>
                </div>
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-2">
                            <label for="department_id" class="form-label">القسم</label>
                            <select class="form-select" id="department_id" name="department_id">
                                <option value="">جميع الأقسام</option>
                                <?php foreach ($departments as $dept): ?>
                                    <option value="<?php echo $dept['id']; ?>" 
                                            <?php echo $department_id == $dept['id'] ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($dept['name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="position_id" class="form-label">المنصب</label>
                            <select class="form-select" id="position_id" name="position_id">
                                <option value="">جميع المناصب</option>
                                <?php foreach ($positions as $pos): ?>
                                    <option value="<?php echo $pos['id']; ?>" 
                                            <?php echo $position_id == $pos['id'] ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($pos['title']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="status" class="form-label">الحالة</label>
                            <select class="form-select" id="status" name="status">
                                <option value="">جميع الحالات</option>
                                <option value="active" <?php echo $status == 'active' ? 'selected' : ''; ?>>نشط</option>
                                <option value="terminated" <?php echo $status == 'terminated' ? 'selected' : ''; ?>>منتهي الخدمة</option>
                                <option value="suspended" <?php echo $status == 'suspended' ? 'selected' : ''; ?>>موقوف</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="gender" class="form-label">الجنس</label>
                            <select class="form-select" id="gender" name="gender">
                                <option value="">الكل</option>
                                <option value="male" <?php echo $gender == 'male' ? 'selected' : ''; ?>>ذكر</option>
                                <option value="female" <?php echo $gender == 'female' ? 'selected' : ''; ?>>أنثى</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="qualification" class="form-label">المؤهل</label>
                            <select class="form-select" id="qualification" name="qualification">
                                <option value="">جميع المؤهلات</option>
                                <option value="ثانوية" <?php echo $qualification == 'ثانوية' ? 'selected' : ''; ?>>ثانوية</option>
                                <option value="دبلوم متوسط" <?php echo $qualification == 'دبلوم متوسط' ? 'selected' : ''; ?>>دبلوم متوسط</option>
                                <option value="دبلوم عالي" <?php echo $qualification == 'دبلوم عالي' ? 'selected' : ''; ?>>دبلوم عالي</option>
                                <option value="بكالوريوس" <?php echo $qualification == 'بكالوريوس' ? 'selected' : ''; ?>>بكالوريوس</option>
                                <option value="ليسانس" <?php echo $qualification == 'ليسانس' ? 'selected' : ''; ?>>ليسانس</option>
                                <option value="ماجستير" <?php echo $qualification == 'ماجستير' ? 'selected' : ''; ?>>ماجستير</option>
                                <option value="دكتوراة" <?php echo $qualification == 'دكتوراة' ? 'selected' : ''; ?>>دكتوراة</option>
                            </select>
                        </div>
                        <div class="col-md-2 d-flex align-items-end">
                            <button type="submit" class="btn btn-primary me-2">
                                <i class="fas fa-search me-1"></i>
                                تطبيق
                            </button>
                            <a href="employees.php" class="btn btn-outline-secondary">
                                <i class="fas fa-undo me-1"></i>
                                إعادة تعيين
                            </a>
                        </div>
                    </form>
                </div>
            </div>

            <!-- إحصائيات الموظفين -->
            <div class="row mb-4">
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card stats-card border-0 shadow-sm">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                        إجمالي الموظفين
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800 counter">
                                        <?php echo $total_employees; ?>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-users fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card stats-card success border-0 shadow-sm">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                        الموظفين النشطين
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800 counter">
                                        <?php echo $active_employees; ?>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-user-check fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card stats-card info border-0 shadow-sm">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                        الموظفين الذكور
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800 counter">
                                        <?php echo $male_employees; ?>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-male fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card stats-card warning border-0 shadow-sm">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                        الموظفات الإناث
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800 counter">
                                        <?php echo $female_employees; ?>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-female fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mb-4">
                <!-- توزيع المؤهلات -->
                <div class="col-lg-6">
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">
                                <i class="fas fa-graduation-cap me-2"></i>
                                توزيع المؤهلات العلمية
                            </h6>
                        </div>
                        <div class="card-body">
                            <canvas id="qualificationsChart" width="100%" height="50"></canvas>
                        </div>
                    </div>
                </div>

                <!-- توزيع الأعمار -->
                <div class="col-lg-6">
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">
                                <i class="fas fa-birthday-cake me-2"></i>
                                توزيع الفئات العمرية
                            </h6>
                        </div>
                        <div class="card-body">
                            <canvas id="ageGroupsChart" width="100%" height="50"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- جدول الموظفين -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-table me-2"></i>
                        قائمة الموظفين التفصيلية
                        <small class="text-muted">(<?php echo count($employees); ?> موظف)</small>
                    </h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered data-table" id="employeesTable" width="100%" cellspacing="0">
                            <thead>
                                <tr>
                                    <th>الرقم الوظيفي</th>
                                    <th>الاسم</th>
                                    <th>القسم</th>
                                    <th>المنصب</th>
                                    <th>المؤهل</th>
                                    <th>العمر</th>
                                    <th>سنوات الخدمة</th>
                                    <th>الحالة</th>
                                    <th>تاريخ التوظيف</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($employees as $employee): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($employee['employee_number']); ?></td>
                                    <td>
                                        <div>
                                            <strong><?php echo htmlspecialchars($employee['first_name'] . ' ' . $employee['last_name']); ?></strong>
                                            <br><small class="text-muted">
                                                <i class="fas fa-<?php echo $employee['gender'] == 'male' ? 'male' : 'female'; ?> me-1"></i>
                                                <?php echo $employee['gender'] == 'male' ? 'ذكر' : 'أنثى'; ?>
                                            </small>
                                        </div>
                                    </td>
                                    <td><?php echo htmlspecialchars($employee['department_name'] ?: 'غير محدد'); ?></td>
                                    <td><?php echo htmlspecialchars($employee['position_title'] ?: 'غير محدد'); ?></td>
                                    <td><?php echo htmlspecialchars($employee['qualification'] ?: 'غير محدد'); ?></td>
                                    <td>
                                        <?php if ($employee['age']): ?>
                                            <span class="badge bg-info"><?php echo $employee['age']; ?> سنة</span>
                                        <?php else: ?>
                                            <span class="text-muted">غير محدد</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if ($employee['years_of_service']): ?>
                                            <span class="badge bg-success"><?php echo $employee['years_of_service']; ?> سنة</span>
                                        <?php else: ?>
                                            <span class="text-muted">غير محدد</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php
                                        $status_classes = [
                                            'active' => 'success',
                                            'terminated' => 'danger',
                                            'suspended' => 'warning'
                                        ];
                                        $status_text = [
                                            'active' => 'نشط',
                                            'terminated' => 'منتهي الخدمة',
                                            'suspended' => 'موقوف'
                                        ];
                                        $status_class = $status_classes[$employee['status']] ?? 'secondary';
                                        ?>
                                        <span class="badge bg-<?php echo $status_class; ?>">
                                            <?php echo $status_text[$employee['status']] ?? $employee['status']; ?>
                                        </span>
                                    </td>
                                    <td><?php echo formatDate($employee['hire_date']); ?></td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<script>
// بيانات المؤهلات
const qualificationsData = {
    labels: [
        <?php foreach ($qualifications_stats as $qual => $count): ?>
            '<?php echo htmlspecialchars($qual); ?>',
        <?php endforeach; ?>
    ],
    datasets: [{
        data: [
            <?php foreach ($qualifications_stats as $qual => $count): ?>
                <?php echo $count; ?>,
            <?php endforeach; ?>
        ],
        backgroundColor: [
            '#4e73df', '#1cc88a', '#36b9cc', '#f6c23e', '#e74a3b',
            '#858796', '#5a5c69', '#6f42c1', '#e83e8c'
        ]
    }]
};

// بيانات الفئات العمرية
const ageGroupsData = {
    labels: ['20-30', '31-40', '41-50', '51-60', '60+'],
    datasets: [{
        label: 'عدد الموظفين',
        data: [
            <?php foreach ($age_groups as $group => $count): ?>
                <?php echo $count; ?>,
            <?php endforeach; ?>
        ],
        backgroundColor: '#4e73df',
        borderColor: '#4e73df',
        borderWidth: 1
    }]
};

// رسم الرسوم البيانية
$(document).ready(function() {
    // رسم بياني للمؤهلات
    const qualCtx = document.getElementById('qualificationsChart').getContext('2d');
    new Chart(qualCtx, {
        type: 'pie',
        data: qualificationsData,
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'bottom',
                }
            }
        }
    });

    // رسم بياني للفئات العمرية
    const ageCtx = document.getElementById('ageGroupsChart').getContext('2d');
    new Chart(ageCtx, {
        type: 'bar',
        data: ageGroupsData,
        options: {
            responsive: true,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        stepSize: 1
                    }
                }
            }
        }
    });

    // تهيئة DataTable
    $('#employeesTable').DataTable({
        language: {
            url: 'https://cdn.datatables.net/plug-ins/1.13.6/i18n/ar.json'
        },
        responsive: true,
        pageLength: 25,
        order: [[1, 'asc']],
        dom: 'Bfrtip',
        buttons: [
            'copy', 'csv', 'excel', 'pdf', 'print'
        ]
    });
});

// تصدير تقرير الموظفين
function exportEmployees(format) {
    const params = new URLSearchParams(window.location.search);
    params.set('export', format);
    
    showLoading('جاري تصدير التقرير...');
    
    fetch('export_employees.php?' + params.toString())
        .then(response => response.blob())
        .then(blob => {
            hideLoading();
            
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `employees_report_${new Date().toISOString().split('T')[0]}.${format}`;
            a.click();
            window.URL.revokeObjectURL(url);
            
            showSuccess('تم تصدير التقرير بنجاح');
        })
        .catch(error => {
            hideLoading();
            showError('حدث خطأ أثناء تصدير التقرير');
        });
}
</script>

<?php include '../includes/footer.php'; ?>
