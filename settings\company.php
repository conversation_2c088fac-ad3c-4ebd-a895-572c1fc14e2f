<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// التحقق من تسجيل الدخول والصلاحيات
if (!isset($_SESSION['user_id']) || !in_array($_SESSION['role'], ['admin', 'hr'])) {
    header('Location: ../login.php');
    exit();
}

$page_title = 'إعدادات الشركة';
$success_message = '';
$error_message = '';

try {
    $pdo = getConnection();
    
    // الحصول على إعدادات الشركة الحالية
    $settings_stmt = $pdo->query("SELECT * FROM company_settings LIMIT 1");
    $company_settings = $settings_stmt->fetch();
    
    // إذا لم توجد إعدادات، إنشاء سجل افتراضي
    if (!$company_settings) {
        $pdo->query("
            INSERT INTO company_settings (
                company_name, company_name_en, address, phone, email, 
                website, tax_number, commercial_register, 
                work_start_time, work_end_time, weekend_days,
                created_at, updated_at
            ) VALUES (
                'اسم الشركة', 'Company Name', 'العنوان', '*********', '<EMAIL>',
                'www.company.com', '*********', '*********',
                '08:00:00', '17:00:00', 'friday,saturday',
                NOW(), NOW()
            )
        ");
        
        $settings_stmt = $pdo->query("SELECT * FROM company_settings LIMIT 1");
        $company_settings = $settings_stmt->fetch();
    }
    
} catch (PDOException $e) {
    $company_settings = [];
}

// معالجة تحديث الإعدادات
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    try {
        $pdo = getConnection();
        
        // التحقق من صحة البيانات
        $required_fields = ['company_name', 'company_name_en', 'address', 'phone', 'email'];
        foreach ($required_fields as $field) {
            if (empty($_POST[$field])) {
                throw new Exception('يرجى ملء جميع الحقول المطلوبة');
            }
        }
        
        // تحضير البيانات
        $company_name = sanitizeInput($_POST['company_name']);
        $company_name_en = sanitizeInput($_POST['company_name_en']);
        $address = sanitizeInput($_POST['address']);
        $phone = sanitizeInput($_POST['phone']);
        $email = sanitizeInput($_POST['email']);
        $website = sanitizeInput($_POST['website']);
        $tax_number = sanitizeInput($_POST['tax_number']);
        $commercial_register = sanitizeInput($_POST['commercial_register']);
        $work_start_time = $_POST['work_start_time'];
        $work_end_time = $_POST['work_end_time'];
        $weekend_days = implode(',', $_POST['weekend_days'] ?? []);
        $currency = sanitizeInput($_POST['currency']);
        $timezone = sanitizeInput($_POST['timezone']);
        $language = sanitizeInput($_POST['language']);
        
        // معالجة رفع الشعار
        $logo_path = $company_settings['logo'] ?? '';
        if (isset($_FILES['logo']) && $_FILES['logo']['error'] == 0) {
            $upload_dir = '../uploads/company/';
            if (!file_exists($upload_dir)) {
                mkdir($upload_dir, 0777, true);
            }
            
            $file_extension = strtolower(pathinfo($_FILES['logo']['name'], PATHINFO_EXTENSION));
            $allowed_extensions = ['jpg', 'jpeg', 'png', 'gif'];
            
            if (in_array($file_extension, $allowed_extensions)) {
                $new_filename = 'logo_' . time() . '.' . $file_extension;
                $upload_path = $upload_dir . $new_filename;
                
                if (move_uploaded_file($_FILES['logo']['tmp_name'], $upload_path)) {
                    // حذف الشعار القديم
                    if ($logo_path && file_exists($logo_path)) {
                        unlink($logo_path);
                    }
                    $logo_path = $upload_path;
                }
            }
        }
        
        // تحديث الإعدادات
        $update_stmt = $pdo->prepare("
            UPDATE company_settings SET
                company_name = ?, company_name_en = ?, address = ?, phone = ?, email = ?,
                website = ?, tax_number = ?, commercial_register = ?, logo = ?,
                work_start_time = ?, work_end_time = ?, weekend_days = ?,
                currency = ?, timezone = ?, language = ?, updated_at = NOW()
            WHERE id = ?
        ");
        
        $update_stmt->execute([
            $company_name, $company_name_en, $address, $phone, $email,
            $website, $tax_number, $commercial_register, $logo_path,
            $work_start_time, $work_end_time, $weekend_days,
            $currency, $timezone, $language, $company_settings['id']
        ]);
        
        // تسجيل النشاط
        logActivity($_SESSION['user_id'], 'تحديث إعدادات الشركة', 'تم تحديث إعدادات الشركة');
        
        $success_message = 'تم تحديث إعدادات الشركة بنجاح';
        
        // إعادة تحميل الإعدادات
        $settings_stmt = $pdo->query("SELECT * FROM company_settings LIMIT 1");
        $company_settings = $settings_stmt->fetch();
        
    } catch (Exception $e) {
        $error_message = $e->getMessage();
    }
}

include '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
            <div class="position-sticky pt-3">
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link" href="../index.php">
                            <i class="fas fa-tachometer-alt me-2"></i>
                            لوحة التحكم
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="company.php">
                            <i class="fas fa-building me-2"></i>
                            إعدادات الشركة
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="departments.php">
                            <i class="fas fa-sitemap me-2"></i>
                            إدارة الأقسام
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="positions.php">
                            <i class="fas fa-user-tie me-2"></i>
                            المسميات الوظيفية
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="leave_types.php">
                            <i class="fas fa-calendar-alt me-2"></i>
                            أنواع الإجازات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="users.php">
                            <i class="fas fa-users-cog me-2"></i>
                            إدارة المستخدمين
                        </a>
                    </li>
                </ul>
            </div>
        </nav>
        
        <!-- Main Content -->
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">
                    <i class="fas fa-building me-2"></i>
                    إعدادات الشركة
                </h1>
            </div>

            <?php if ($success_message): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle me-2"></i>
                    <?php echo $success_message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?php if ($error_message): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <?php echo $error_message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <div class="row">
                <div class="col-lg-8">
                    <!-- نموذج إعدادات الشركة -->
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">
                                <i class="fas fa-cog me-2"></i>
                                معلومات الشركة الأساسية
                            </h6>
                        </div>
                        <div class="card-body">
                            <form method="POST" enctype="multipart/form-data" class="needs-validation" novalidate>
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="company_name" class="form-label">اسم الشركة (عربي) <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" id="company_name" name="company_name" 
                                               value="<?php echo htmlspecialchars($company_settings['company_name'] ?? ''); ?>" required>
                                        <div class="invalid-feedback">يرجى إدخال اسم الشركة</div>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="company_name_en" class="form-label">اسم الشركة (إنجليزي) <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" id="company_name_en" name="company_name_en" 
                                               value="<?php echo htmlspecialchars($company_settings['company_name_en'] ?? ''); ?>" required>
                                        <div class="invalid-feedback">يرجى إدخال اسم الشركة بالإنجليزية</div>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="address" class="form-label">العنوان <span class="text-danger">*</span></label>
                                    <textarea class="form-control" id="address" name="address" rows="3" required><?php echo htmlspecialchars($company_settings['address'] ?? ''); ?></textarea>
                                    <div class="invalid-feedback">يرجى إدخال عنوان الشركة</div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="phone" class="form-label">رقم الهاتف <span class="text-danger">*</span></label>
                                        <input type="tel" class="form-control" id="phone" name="phone" 
                                               value="<?php echo htmlspecialchars($company_settings['phone'] ?? ''); ?>" required>
                                        <div class="invalid-feedback">يرجى إدخال رقم الهاتف</div>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="email" class="form-label">البريد الإلكتروني <span class="text-danger">*</span></label>
                                        <input type="email" class="form-control" id="email" name="email" 
                                               value="<?php echo htmlspecialchars($company_settings['email'] ?? ''); ?>" required>
                                        <div class="invalid-feedback">يرجى إدخال بريد إلكتروني صحيح</div>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="website" class="form-label">الموقع الإلكتروني</label>
                                        <input type="url" class="form-control" id="website" name="website" 
                                               value="<?php echo htmlspecialchars($company_settings['website'] ?? ''); ?>">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="tax_number" class="form-label">الرقم الضريبي</label>
                                        <input type="text" class="form-control" id="tax_number" name="tax_number" 
                                               value="<?php echo htmlspecialchars($company_settings['tax_number'] ?? ''); ?>">
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="commercial_register" class="form-label">السجل التجاري</label>
                                        <input type="text" class="form-control" id="commercial_register" name="commercial_register" 
                                               value="<?php echo htmlspecialchars($company_settings['commercial_register'] ?? ''); ?>">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="logo" class="form-label">شعار الشركة</label>
                                        <input type="file" class="form-control" id="logo" name="logo" accept="image/*">
                                        <small class="form-text text-muted">يُفضل أن يكون الشعار بصيغة PNG أو JPG</small>
                                    </div>
                                </div>
                                
                                <hr class="my-4">
                                
                                <h6 class="text-primary mb-3">
                                    <i class="fas fa-clock me-2"></i>
                                    إعدادات أوقات العمل
                                </h6>
                                
                                <div class="row">
                                    <div class="col-md-4 mb-3">
                                        <label for="work_start_time" class="form-label">بداية الدوام</label>
                                        <input type="time" class="form-control" id="work_start_time" name="work_start_time" 
                                               value="<?php echo $company_settings['work_start_time'] ?? '08:00'; ?>">
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <label for="work_end_time" class="form-label">نهاية الدوام</label>
                                        <input type="time" class="form-control" id="work_end_time" name="work_end_time" 
                                               value="<?php echo $company_settings['work_end_time'] ?? '17:00'; ?>">
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <label class="form-label">أيام العطلة الأسبوعية</label>
                                        <?php 
                                        $weekend_days = explode(',', $company_settings['weekend_days'] ?? 'friday,saturday');
                                        $days = [
                                            'sunday' => 'الأحد',
                                            'monday' => 'الاثنين',
                                            'tuesday' => 'الثلاثاء',
                                            'wednesday' => 'الأربعاء',
                                            'thursday' => 'الخميس',
                                            'friday' => 'الجمعة',
                                            'saturday' => 'السبت'
                                        ];
                                        ?>
                                        <?php foreach ($days as $day_en => $day_ar): ?>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="weekend_days[]" 
                                                   value="<?php echo $day_en; ?>" id="<?php echo $day_en; ?>"
                                                   <?php echo in_array($day_en, $weekend_days) ? 'checked' : ''; ?>>
                                            <label class="form-check-label" for="<?php echo $day_en; ?>">
                                                <?php echo $day_ar; ?>
                                            </label>
                                        </div>
                                        <?php endforeach; ?>
                                    </div>
                                </div>
                                
                                <hr class="my-4">
                                
                                <h6 class="text-primary mb-3">
                                    <i class="fas fa-globe me-2"></i>
                                    الإعدادات العامة
                                </h6>
                                
                                <div class="row">
                                    <div class="col-md-4 mb-3">
                                        <label for="currency" class="form-label">العملة</label>
                                        <select class="form-select" id="currency" name="currency">
                                            <option value="SAR" <?php echo ($company_settings['currency'] ?? 'SAR') == 'SAR' ? 'selected' : ''; ?>>ريال سعودي (SAR)</option>
                                            <option value="USD" <?php echo ($company_settings['currency'] ?? '') == 'USD' ? 'selected' : ''; ?>>دولار أمريكي (USD)</option>
                                            <option value="EUR" <?php echo ($company_settings['currency'] ?? '') == 'EUR' ? 'selected' : ''; ?>>يورو (EUR)</option>
                                            <option value="AED" <?php echo ($company_settings['currency'] ?? '') == 'AED' ? 'selected' : ''; ?>>درهم إماراتي (AED)</option>
                                        </select>
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <label for="timezone" class="form-label">المنطقة الزمنية</label>
                                        <select class="form-select" id="timezone" name="timezone">
                                            <option value="Asia/Riyadh" <?php echo ($company_settings['timezone'] ?? 'Asia/Riyadh') == 'Asia/Riyadh' ? 'selected' : ''; ?>>الرياض</option>
                                            <option value="Asia/Dubai" <?php echo ($company_settings['timezone'] ?? '') == 'Asia/Dubai' ? 'selected' : ''; ?>>دبي</option>
                                            <option value="Asia/Kuwait" <?php echo ($company_settings['timezone'] ?? '') == 'Asia/Kuwait' ? 'selected' : ''; ?>>الكويت</option>
                                            <option value="Asia/Qatar" <?php echo ($company_settings['timezone'] ?? '') == 'Asia/Qatar' ? 'selected' : ''; ?>>قطر</option>
                                        </select>
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <label for="language" class="form-label">اللغة الافتراضية</label>
                                        <select class="form-select" id="language" name="language">
                                            <option value="ar" <?php echo ($company_settings['language'] ?? 'ar') == 'ar' ? 'selected' : ''; ?>>العربية</option>
                                            <option value="en" <?php echo ($company_settings['language'] ?? '') == 'en' ? 'selected' : ''; ?>>English</option>
                                        </select>
                                    </div>
                                </div>
                                
                                <div class="text-center">
                                    <button type="submit" class="btn btn-primary btn-lg">
                                        <i class="fas fa-save me-2"></i>
                                        حفظ الإعدادات
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-4">
                    <!-- معاينة الشعار -->
                    <?php if (!empty($company_settings['logo']) && file_exists($company_settings['logo'])): ?>
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">
                                <i class="fas fa-image me-2"></i>
                                الشعار الحالي
                            </h6>
                        </div>
                        <div class="card-body text-center">
                            <img src="<?php echo $company_settings['logo']; ?>" alt="شعار الشركة" 
                                 class="img-fluid" style="max-height: 200px;">
                        </div>
                    </div>
                    <?php endif; ?>
                    
                    <!-- معلومات إضافية -->
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">
                                <i class="fas fa-info-circle me-2"></i>
                                معلومات مهمة
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-info">
                                <h6><i class="fas fa-lightbulb me-2"></i>نصائح:</h6>
                                <ul class="mb-0 small">
                                    <li>تأكد من صحة جميع المعلومات قبل الحفظ</li>
                                    <li>يُفضل أن يكون الشعار بخلفية شفافة</li>
                                    <li>أوقات العمل تؤثر على حساب التأخير والساعات الإضافية</li>
                                    <li>أيام العطلة لن تُحسب في تقارير الحضور</li>
                                </ul>
                            </div>
                            
                            <?php if (!empty($company_settings['updated_at'])): ?>
                            <div class="text-muted small">
                                <i class="fas fa-clock me-1"></i>
                                آخر تحديث: <?php echo formatDateTime($company_settings['updated_at']); ?>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<script>
// تفعيل التحقق من صحة النموذج
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();

// معاينة الشعار قبل الرفع
document.getElementById('logo').addEventListener('change', function(e) {
    const file = e.target.files[0];
    if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            // يمكن إضافة معاينة هنا
        };
        reader.readAsDataURL(file);
    }
});
</script>

<?php include '../includes/footer.php'; ?>
