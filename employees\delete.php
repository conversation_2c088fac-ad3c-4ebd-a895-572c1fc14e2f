<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

header('Content-Type: application/json');

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    echo json_encode(['success' => false, 'message' => 'غير مصرح لك بالوصول']);
    exit();
}

// التحقق من صحة الطلب
if ($_SERVER['REQUEST_METHOD'] !== 'POST' || !isset($_POST['id']) || !is_numeric($_POST['id'])) {
    echo json_encode(['success' => false, 'message' => 'طلب غير صحيح']);
    exit();
}

$employee_id = (int)$_POST['id'];

try {
    $pdo = getConnection();
    
    // التحقق من وجود الموظف
    $check_stmt = $pdo->prepare("SELECT first_name, last_name FROM employees WHERE id = ?");
    $check_stmt->execute([$employee_id]);
    $employee = $check_stmt->fetch();
    
    if (!$employee) {
        echo json_encode(['success' => false, 'message' => 'الموظف غير موجود']);
        exit();
    }
    
    // التحقق من وجود سجلات مرتبطة
    $attendance_stmt = $pdo->prepare("SELECT COUNT(*) FROM attendance WHERE employee_id = ?");
    $attendance_stmt->execute([$employee_id]);
    $attendance_count = $attendance_stmt->fetchColumn();
    
    $leave_stmt = $pdo->prepare("SELECT COUNT(*) FROM leave_requests WHERE employee_id = ?");
    $leave_stmt->execute([$employee_id]);
    $leave_count = $leave_stmt->fetchColumn();
    
    // إذا كان هناك سجلات مرتبطة، تغيير الحالة بدلاً من الحذف
    if ($attendance_count > 0 || $leave_count > 0) {
        $update_stmt = $pdo->prepare("UPDATE employees SET status = 'terminated', updated_at = NOW() WHERE id = ?");
        $update_stmt->execute([$employee_id]);
        
        // تسجيل التغيير في سجل التغييرات
        $history_stmt = $pdo->prepare("
            INSERT INTO employee_status_history (
                employee_id, change_type, old_value, new_value, 
                effective_date, reason, created_by, created_at
            ) VALUES (?, 'إنهاء خدمة', 'نشط', 'منتهي الخدمة', CURDATE(), 'حذف من النظام', ?, NOW())
        ");
        $history_stmt->execute([$employee_id, $_SESSION['user_id']]);
        
        $message = 'تم إنهاء خدمة الموظف بنجاح (لا يمكن الحذف نهائياً لوجود سجلات مرتبطة)';
    } else {
        // بدء المعاملة
        $pdo->beginTransaction();
        
        try {
            // حذف المستندات
            $docs_stmt = $pdo->prepare("SELECT file_path FROM employee_documents WHERE employee_id = ?");
            $docs_stmt->execute([$employee_id]);
            $documents = $docs_stmt->fetchAll();
            
            foreach ($documents as $doc) {
                if (file_exists($doc['file_path'])) {
                    unlink($doc['file_path']);
                }
            }
            
            // حذف سجلات قاعدة البيانات المرتبطة
            $pdo->prepare("DELETE FROM employee_documents WHERE employee_id = ?")->execute([$employee_id]);
            $pdo->prepare("DELETE FROM employee_status_history WHERE employee_id = ?")->execute([$employee_id]);
            $pdo->prepare("DELETE FROM leave_balances WHERE employee_id = ?")->execute([$employee_id]);
            
            // حذف الموظف
            $delete_stmt = $pdo->prepare("DELETE FROM employees WHERE id = ?");
            $delete_stmt->execute([$employee_id]);
            
            // تأكيد المعاملة
            $pdo->commit();
            
            $message = 'تم حذف الموظف نهائياً من النظام';
        } catch (Exception $e) {
            // التراجع عن المعاملة
            $pdo->rollback();
            throw $e;
        }
    }
    
    // تسجيل النشاط
    logActivity(
        $_SESSION['user_id'], 
        'حذف موظف', 
        "تم حذف الموظف: {$employee['first_name']} {$employee['last_name']}"
    );
    
    echo json_encode([
        'success' => true, 
        'message' => $message
    ]);
    
} catch (PDOException $e) {
    echo json_encode([
        'success' => false, 
        'message' => 'حدث خطأ في قاعدة البيانات: ' . $e->getMessage()
    ]);
} catch (Exception $e) {
    echo json_encode([
        'success' => false, 
        'message' => 'حدث خطأ: ' . $e->getMessage()
    ]);
}
?>
