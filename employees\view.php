<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit();
}

// التحقق من وجود معرف الموظف
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    header('Location: list.php');
    exit();
}

$employee_id = (int)$_GET['id'];
$page_title = 'تفاصيل الموظف';

try {
    $pdo = getConnection();
    
    // الحصول على بيانات الموظف
    $stmt = $pdo->prepare("
        SELECT 
            e.*,
            d.name as department_name,
            p.title as position_title
        FROM employees e
        LEFT JOIN departments d ON e.department_id = d.id
        LEFT JOIN positions p ON e.position_id = p.id
        WHERE e.id = ?
    ");
    $stmt->execute([$employee_id]);
    $employee = $stmt->fetch();
    
    if (!$employee) {
        header('Location: list.php');
        exit();
    }
    
    // الحصول على مستندات الموظف
    $docs_stmt = $pdo->prepare("
        SELECT * FROM employee_documents 
        WHERE employee_id = ? 
        ORDER BY upload_date DESC
    ");
    $docs_stmt->execute([$employee_id]);
    $documents = $docs_stmt->fetchAll();
    
    // الحصول على سجل التغييرات
    $history_stmt = $pdo->prepare("
        SELECT 
            esh.*,
            u.username as created_by_name
        FROM employee_status_history esh
        LEFT JOIN users u ON esh.created_by = u.id
        WHERE esh.employee_id = ?
        ORDER BY esh.created_at DESC
        LIMIT 10
    ");
    $history_stmt->execute([$employee_id]);
    $status_history = $history_stmt->fetchAll();
    
    // الحصول على أرصدة الإجازات
    $leave_stmt = $pdo->prepare("
        SELECT 
            lb.*,
            lt.name as leave_type_name
        FROM leave_balances lb
        JOIN leave_types lt ON lb.leave_type_id = lt.id
        WHERE lb.employee_id = ? AND lb.year = YEAR(CURDATE())
    ");
    $leave_stmt->execute([$employee_id]);
    $leave_balances = $leave_stmt->fetchAll();
    
} catch (PDOException $e) {
    $error_message = 'خطأ في تحميل بيانات الموظف';
    $employee = null;
}

include '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
            <div class="position-sticky pt-3">
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link" href="../index.php">
                            <i class="fas fa-tachometer-alt me-2"></i>
                            لوحة التحكم
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="list.php">
                            <i class="fas fa-users me-2"></i>
                            قائمة الموظفين
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="add.php">
                            <i class="fas fa-plus me-2"></i>
                            إضافة موظف
                        </a>
                    </li>
                </ul>
            </div>
        </nav>
        
        <!-- Main Content -->
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <?php if ($employee): ?>
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">
                    <i class="fas fa-user me-2"></i>
                    <?php echo htmlspecialchars($employee['first_name'] . ' ' . $employee['last_name']); ?>
                </h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <a href="edit.php?id=<?php echo $employee['id']; ?>" class="btn btn-primary">
                            <i class="fas fa-edit me-1"></i>
                            تعديل
                        </a>
                        <a href="list.php" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-right me-1"></i>
                            العودة للقائمة
                        </a>
                    </div>
                </div>
            </div>

            <!-- معلومات أساسية -->
            <div class="row mb-4">
                <div class="col-xl-4 col-lg-5">
                    <div class="card shadow mb-4">
                        <div class="card-body text-center">
                            <div class="avatar-lg mx-auto mb-3">
                                <div class="avatar-title bg-primary rounded-circle">
                                    <?php echo strtoupper(substr($employee['first_name'], 0, 1) . substr($employee['last_name'], 0, 1)); ?>
                                </div>
                            </div>
                            <h5 class="card-title"><?php echo htmlspecialchars($employee['first_name'] . ' ' . $employee['last_name']); ?></h5>
                            <p class="text-muted"><?php echo htmlspecialchars($employee['position_title'] ?: 'غير محدد'); ?></p>
                            <p class="text-muted"><?php echo htmlspecialchars($employee['department_name'] ?: 'غير محدد'); ?></p>
                            
                            <?php
                            $status_classes = [
                                'active' => 'success',
                                'inactive' => 'warning',
                                'terminated' => 'danger',
                                'resigned' => 'secondary'
                            ];
                            $status_labels = [
                                'active' => 'نشط',
                                'inactive' => 'غير نشط',
                                'terminated' => 'منتهي الخدمة',
                                'resigned' => 'مستقيل'
                            ];
                            $status_class = $status_classes[$employee['status']] ?? 'secondary';
                            $status_label = $status_labels[$employee['status']] ?? $employee['status'];
                            ?>
                            <span class="badge bg-<?php echo $status_class; ?> badge-lg">
                                <?php echo $status_label; ?>
                            </span>
                            
                            <hr>
                            
                            <div class="row text-center">
                                <div class="col-6">
                                    <strong><?php echo calculateAge($employee['birth_date']); ?></strong>
                                    <p class="text-muted small">العمر</p>
                                </div>
                                <div class="col-6">
                                    <strong><?php echo calculateServiceYears($employee['hire_date']); ?></strong>
                                    <p class="text-muted small">سنوات الخدمة</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-xl-8 col-lg-7">
                    <!-- البيانات الشخصية -->
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">
                                <i class="fas fa-user me-2"></i>
                                البيانات الشخصية
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <strong>الرقم الوظيفي:</strong>
                                    <p><?php echo htmlspecialchars($employee['employee_number']); ?></p>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <strong>الرقم الوطني:</strong>
                                    <p><?php echo htmlspecialchars($employee['national_id']); ?></p>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <strong>تاريخ الميلاد:</strong>
                                    <p><?php echo formatDate($employee['birth_date']); ?></p>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <strong>المدينة:</strong>
                                    <p><?php echo htmlspecialchars($employee['city'] ?: 'غير محدد'); ?></p>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <strong>رقم الهاتف:</strong>
                                    <p><?php echo htmlspecialchars($employee['phone'] ?: 'غير محدد'); ?></p>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <strong>البريد الإلكتروني:</strong>
                                    <p><?php echo htmlspecialchars($employee['email'] ?: 'غير محدد'); ?></p>
                                </div>
                            </div>
                            
                            <?php if ($employee['address']): ?>
                            <div class="mb-3">
                                <strong>العنوان:</strong>
                                <p><?php echo htmlspecialchars($employee['address']); ?></p>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- البيانات الوظيفية -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-briefcase me-2"></i>
                        البيانات الوظيفية
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <strong>تاريخ التوظيف:</strong>
                            <p><?php echo formatDate($employee['hire_date']); ?></p>
                        </div>
                        <div class="col-md-3 mb-3">
                            <strong>بداية العقد:</strong>
                            <p><?php echo $employee['contract_start_date'] ? formatDate($employee['contract_start_date']) : 'غير محدد'; ?></p>
                        </div>
                        <div class="col-md-3 mb-3">
                            <strong>نهاية العقد:</strong>
                            <p><?php echo $employee['contract_end_date'] ? formatDate($employee['contract_end_date']) : 'غير محدد'; ?></p>
                        </div>
                        <div class="col-md-3 mb-3">
                            <strong>المؤهل العلمي:</strong>
                            <p><?php echo htmlspecialchars($employee['education_level'] ?: 'غير محدد'); ?></p>
                        </div>
                    </div>
                    
                    <?php if ($employee['salary']): ?>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <strong>الراتب:</strong>
                            <p><?php echo number_format($employee['salary'], 2); ?> ريال</p>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- أرصدة الإجازات -->
            <?php if (!empty($leave_balances)): ?>
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-calendar-times me-2"></i>
                        أرصدة الإجازات (<?php echo date('Y'); ?>)
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <?php foreach ($leave_balances as $balance): ?>
                        <div class="col-md-4 mb-3">
                            <div class="card border-left-info">
                                <div class="card-body">
                                    <h6 class="card-title"><?php echo htmlspecialchars($balance['leave_type_name']); ?></h6>
                                    <div class="progress mb-2">
                                        <?php 
                                        $percentage = $balance['total_days'] > 0 ? ($balance['remaining_days'] / $balance['total_days']) * 100 : 0;
                                        ?>
                                        <div class="progress-bar bg-info" style="width: <?php echo $percentage; ?>%"></div>
                                    </div>
                                    <small class="text-muted">
                                        المتبقي: <?php echo $balance['remaining_days']; ?> من <?php echo $balance['total_days']; ?> يوم
                                    </small>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
            <?php endif; ?>

            <!-- المستندات -->
            <?php if (!empty($documents)): ?>
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-file-alt me-2"></i>
                        المستندات
                    </h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead>
                                <tr>
                                    <th>نوع المستند</th>
                                    <th>اسم الملف</th>
                                    <th>تاريخ الرفع</th>
                                    <th>الحجم</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($documents as $doc): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($doc['document_type']); ?></td>
                                    <td><?php echo htmlspecialchars($doc['document_name']); ?></td>
                                    <td><?php echo formatDate($doc['upload_date'], 'Y-m-d H:i'); ?></td>
                                    <td><?php echo $doc['file_size'] ? number_format($doc['file_size'] / 1024, 2) . ' KB' : 'غير محدد'; ?></td>
                                    <td>
                                        <a href="<?php echo htmlspecialchars($doc['file_path']); ?>" 
                                           class="btn btn-sm btn-outline-primary" 
                                           target="_blank">
                                            <i class="fas fa-download"></i>
                                        </a>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <?php endif; ?>

            <!-- سجل التغييرات -->
            <?php if (!empty($status_history)): ?>
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-history me-2"></i>
                        سجل التغييرات
                    </h6>
                </div>
                <div class="card-body">
                    <div class="timeline">
                        <?php foreach ($status_history as $history): ?>
                        <div class="timeline-item">
                            <div class="timeline-marker bg-primary"></div>
                            <div class="timeline-content">
                                <h6 class="timeline-title"><?php echo htmlspecialchars($history['change_type']); ?></h6>
                                <p class="timeline-text">
                                    <?php if ($history['old_value'] && $history['new_value']): ?>
                                        من: <?php echo htmlspecialchars($history['old_value']); ?><br>
                                        إلى: <?php echo htmlspecialchars($history['new_value']); ?>
                                    <?php endif; ?>
                                    <?php if ($history['reason']): ?>
                                        <br><strong>السبب:</strong> <?php echo htmlspecialchars($history['reason']); ?>
                                    <?php endif; ?>
                                </p>
                                <small class="text-muted">
                                    <?php echo formatDate($history['created_at'], 'Y-m-d H:i'); ?>
                                    <?php if ($history['created_by_name']): ?>
                                        - بواسطة: <?php echo htmlspecialchars($history['created_by_name']); ?>
                                    <?php endif; ?>
                                </small>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
            <?php endif; ?>

            <?php else: ?>
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle me-2"></i>
                لم يتم العثور على بيانات الموظف المطلوب.
            </div>
            <?php endif; ?>
        </main>
    </div>
</div>

<style>
.avatar-lg {
    width: 80px;
    height: 80px;
}

.avatar-title {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    font-weight: 600;
}

.badge-lg {
    font-size: 0.9rem;
    padding: 0.5rem 1rem;
}

.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #e3e6f0;
}

.timeline-item {
    position: relative;
    margin-bottom: 30px;
}

.timeline-marker {
    position: absolute;
    left: -22px;
    top: 5px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid #fff;
    box-shadow: 0 0 0 2px #e3e6f0;
}

.timeline-content {
    background: #f8f9fc;
    padding: 15px;
    border-radius: 8px;
    border-left: 4px solid var(--bs-primary);
}

.timeline-title {
    margin-bottom: 5px;
    color: var(--bs-primary);
}

.timeline-text {
    margin-bottom: 10px;
    color: #5a5c69;
}

.border-left-info {
    border-right: 4px solid var(--bs-info) !important;
}
</style>

<?php include '../includes/footer.php'; ?>
