<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit();
}

$page_title = 'تقارير الحضور والانصراف';

// معالجة فلاتر البحث
$start_date = $_GET['start_date'] ?? date('Y-m-01'); // بداية الشهر الحالي
$end_date = $_GET['end_date'] ?? date('Y-m-t'); // نهاية الشهر الحالي
$employee_id = $_GET['employee_id'] ?? '';
$department_id = $_GET['department_id'] ?? '';

try {
    $pdo = getConnection();
    
    // الحصول على قائمة الموظفين
    $emp_stmt = $pdo->query("
        SELECT id, employee_number, first_name, last_name 
        FROM employees 
        WHERE status = 'active' 
        ORDER BY first_name, last_name
    ");
    $employees = $emp_stmt->fetchAll();
    
    // الحصول على قائمة الأقسام
    $dept_stmt = $pdo->query("
        SELECT id, name 
        FROM departments 
        WHERE status = 'active' 
        ORDER BY name
    ");
    $departments = $dept_stmt->fetchAll();
    
    // بناء استعلام التقرير
    $where_conditions = ["a.date BETWEEN ? AND ?"];
    $params = [$start_date, $end_date];
    
    if ($employee_id) {
        $where_conditions[] = "a.employee_id = ?";
        $params[] = $employee_id;
    }
    
    if ($department_id) {
        $where_conditions[] = "e.department_id = ?";
        $params[] = $department_id;
    }
    
    $where_clause = implode(' AND ', $where_conditions);
    
    // الحصول على بيانات التقرير
    $report_stmt = $pdo->prepare("
        SELECT 
            a.*,
            e.employee_number,
            e.first_name,
            e.last_name,
            d.name as department_name
        FROM attendance a
        JOIN employees e ON a.employee_id = e.id
        LEFT JOIN departments d ON e.department_id = d.id
        WHERE $where_clause
        ORDER BY a.date DESC, e.first_name
    ");
    $report_stmt->execute($params);
    $attendance_records = $report_stmt->fetchAll();
    
    // حساب الإحصائيات
    $total_records = count($attendance_records);
    $present_count = count(array_filter($attendance_records, function($r) { return $r['check_in']; }));
    $late_count = count(array_filter($attendance_records, function($r) { return $r['late_minutes'] > 0; }));
    $overtime_total = array_sum(array_column($attendance_records, 'overtime_hours'));
    
} catch (PDOException $e) {
    $attendance_records = [];
    $employees = [];
    $departments = [];
}

include '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
            <div class="position-sticky pt-3">
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link" href="../index.php">
                            <i class="fas fa-tachometer-alt me-2"></i>
                            لوحة التحكم
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../employees/list.php">
                            <i class="fas fa-users me-2"></i>
                            قائمة الموظفين
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="record.php">
                            <i class="fas fa-fingerprint me-2"></i>
                            تسجيل الحضور
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="reports.php">
                            <i class="fas fa-chart-bar me-2"></i>
                            تقارير الحضور
                        </a>
                    </li>
                </ul>
            </div>
        </nav>
        
        <!-- Main Content -->
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">
                    <i class="fas fa-chart-bar me-2"></i>
                    تقارير الحضور والانصراف
                </h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <button type="button" class="btn btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
                            <i class="fas fa-download me-1"></i>
                            تصدير التقرير
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" onclick="exportReport('excel')">
                                <i class="fas fa-file-excel me-2"></i>Excel
                            </a></li>
                            <li><a class="dropdown-item" href="#" onclick="exportReport('pdf')">
                                <i class="fas fa-file-pdf me-2"></i>PDF
                            </a></li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- فلاتر التقرير -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-filter me-2"></i>
                        فلاتر التقرير
                    </h6>
                </div>
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-3">
                            <label for="start_date" class="form-label">من تاريخ</label>
                            <input type="date" class="form-control" id="start_date" name="start_date" 
                                   value="<?php echo htmlspecialchars($start_date); ?>">
                        </div>
                        <div class="col-md-3">
                            <label for="end_date" class="form-label">إلى تاريخ</label>
                            <input type="date" class="form-control" id="end_date" name="end_date" 
                                   value="<?php echo htmlspecialchars($end_date); ?>">
                        </div>
                        <div class="col-md-3">
                            <label for="employee_id" class="form-label">الموظف</label>
                            <select class="form-select" id="employee_id" name="employee_id">
                                <option value="">جميع الموظفين</option>
                                <?php foreach ($employees as $emp): ?>
                                    <option value="<?php echo $emp['id']; ?>" 
                                            <?php echo $employee_id == $emp['id'] ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($emp['employee_number'] . ' - ' . $emp['first_name'] . ' ' . $emp['last_name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="department_id" class="form-label">القسم</label>
                            <select class="form-select" id="department_id" name="department_id">
                                <option value="">جميع الأقسام</option>
                                <?php foreach ($departments as $dept): ?>
                                    <option value="<?php echo $dept['id']; ?>" 
                                            <?php echo $department_id == $dept['id'] ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($dept['name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-12">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search me-1"></i>
                                تطبيق الفلاتر
                            </button>
                            <a href="reports.php" class="btn btn-outline-secondary">
                                <i class="fas fa-undo me-1"></i>
                                إعادة تعيين
                            </a>
                        </div>
                    </form>
                </div>
            </div>

            <!-- إحصائيات التقرير -->
            <div class="row mb-4">
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card stats-card border-0 shadow-sm">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                        إجمالي السجلات
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800 counter">
                                        <?php echo $total_records; ?>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-list fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card stats-card success border-0 shadow-sm">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                        أيام الحضور
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800 counter">
                                        <?php echo $present_count; ?>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-user-check fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card stats-card warning border-0 shadow-sm">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                        حالات التأخير
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800 counter">
                                        <?php echo $late_count; ?>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-clock fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card stats-card info border-0 shadow-sm">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                        الساعات الإضافية
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800 counter">
                                        <?php echo number_format($overtime_total, 1); ?>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-plus-circle fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- جدول التقرير -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-table me-2"></i>
                        تفاصيل الحضور والانصراف
                        <small class="text-muted">(من <?php echo $start_date; ?> إلى <?php echo $end_date; ?>)</small>
                    </h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered data-table" id="attendanceReportTable" width="100%" cellspacing="0">
                            <thead>
                                <tr>
                                    <th>التاريخ</th>
                                    <th>الرقم الوظيفي</th>
                                    <th>اسم الموظف</th>
                                    <th>القسم</th>
                                    <th>وقت الحضور</th>
                                    <th>وقت الانصراف</th>
                                    <th>ساعات العمل</th>
                                    <th>التأخير (دقيقة)</th>
                                    <th>الساعات الإضافية</th>
                                    <th>الحالة</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($attendance_records as $record): ?>
                                <tr>
                                    <td><?php echo formatDate($record['date']); ?></td>
                                    <td><?php echo htmlspecialchars($record['employee_number']); ?></td>
                                    <td><?php echo htmlspecialchars($record['first_name'] . ' ' . $record['last_name']); ?></td>
                                    <td><?php echo htmlspecialchars($record['department_name'] ?: 'غير محدد'); ?></td>
                                    <td>
                                        <?php if ($record['check_in']): ?>
                                            <span class="badge bg-success">
                                                <?php echo formatTime($record['check_in']); ?>
                                            </span>
                                        <?php else: ?>
                                            <span class="text-muted">-</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if ($record['check_out']): ?>
                                            <span class="badge bg-danger">
                                                <?php echo formatTime($record['check_out']); ?>
                                            </span>
                                        <?php else: ?>
                                            <span class="text-muted">-</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if ($record['total_hours']): ?>
                                            <?php echo number_format($record['total_hours'], 2); ?>
                                        <?php else: ?>
                                            <span class="text-muted">-</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if ($record['late_minutes'] > 0): ?>
                                            <span class="badge bg-warning">
                                                <?php echo $record['late_minutes']; ?>
                                            </span>
                                        <?php else: ?>
                                            <span class="text-success">0</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if ($record['overtime_hours'] > 0): ?>
                                            <span class="badge bg-info">
                                                <?php echo number_format($record['overtime_hours'], 2); ?>
                                            </span>
                                        <?php else: ?>
                                            <span class="text-muted">0</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php
                                        $status_classes = [
                                            'حاضر' => 'success',
                                            'غائب' => 'danger',
                                            'إجازة' => 'info',
                                            'مرض' => 'warning'
                                        ];
                                        $status_class = $status_classes[$record['status']] ?? 'secondary';
                                        ?>
                                        <span class="badge bg-<?php echo $status_class; ?>">
                                            <?php echo htmlspecialchars($record['status']); ?>
                                        </span>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- رسم بياني للحضور -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-chart-line me-2"></i>
                        إحصائيات الحضور اليومية
                    </h6>
                </div>
                <div class="card-body">
                    <canvas id="attendanceChart" width="100%" height="40"></canvas>
                </div>
            </div>
        </main>
    </div>
</div>

<script>
// تصدير التقرير
function exportReport(format) {
    const params = new URLSearchParams(window.location.search);
    params.set('export', format);
    
    showLoading('جاري تصدير التقرير...');
    
    fetch('export_attendance.php?' + params.toString())
        .then(response => response.blob())
        .then(blob => {
            hideLoading();
            
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `attendance_report_${new Date().toISOString().split('T')[0]}.${format}`;
            a.click();
            window.URL.revokeObjectURL(url);
            
            showSuccess('تم تصدير التقرير بنجاح');
        })
        .catch(error => {
            hideLoading();
            showError('حدث خطأ أثناء تصدير التقرير');
        });
}

// رسم بياني للحضور
$(document).ready(function() {
    // تجميع البيانات حسب التاريخ
    const attendanceData = {};
    <?php foreach ($attendance_records as $record): ?>
        const date = '<?php echo $record['date']; ?>';
        if (!attendanceData[date]) {
            attendanceData[date] = { present: 0, late: 0 };
        }
        <?php if ($record['check_in']): ?>
            attendanceData[date].present++;
        <?php endif; ?>
        <?php if ($record['late_minutes'] > 0): ?>
            attendanceData[date].late++;
        <?php endif; ?>
    <?php endforeach; ?>
    
    const dates = Object.keys(attendanceData).sort();
    const presentData = dates.map(date => attendanceData[date].present);
    const lateData = dates.map(date => attendanceData[date].late);
    
    const ctx = document.getElementById('attendanceChart').getContext('2d');
    new Chart(ctx, {
        type: 'line',
        data: {
            labels: dates,
            datasets: [{
                label: 'الحضور',
                data: presentData,
                borderColor: '#28a745',
                backgroundColor: 'rgba(40, 167, 69, 0.1)',
                borderWidth: 2,
                fill: true
            }, {
                label: 'التأخير',
                data: lateData,
                borderColor: '#ffc107',
                backgroundColor: 'rgba(255, 193, 7, 0.1)',
                borderWidth: 2,
                fill: true
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'top',
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        stepSize: 1
                    }
                }
            }
        }
    });
});
</script>

<?php include '../includes/footer.php'; ?>
