# نظام إدارة شؤون الموظفين

نظام شامل لإدارة شؤون الموظفين مطور بـ PHP و MySQL مع واجهة Bootstrap عربية متجاوبة.

## المميزات الرئيسية

### 1. إدارة الموظفين
- إضافة وتعديل وحذف الموظفين
- معلومات شخصية ووظيفية شاملة
- تحميل ومعاينة مستندات الموظفين
- سجل تغييرات الحالة الوظيفية
- نظام بحث وفلترة متقدم

### 2. إدارة الحضور والانصراف
- تسجيل حضور وانصراف يومي
- حساب ساعات العمل والإضافي
- تتبع التأخير والانصراف المبكر
- تقارير حضور شاملة مع رسوم بيانية

### 3. إدارة الإجازات
- أنواع إجازات متعددة قابلة للتخصيص
- نظام موافقة متعدد المستويات
- تتبع أرصدة الإجازات
- إشعارات تلقائية للطلبات

### 4. الإشعارات والتنبيهات
- إشعارات انتهاء العقود والإقامات
- تنبيهات للمديرين للموافقة على الطلبات
- نظام إشعارات فوري
- تنبيهات البريد الإلكتروني

### 5. نظام التقارير
- تقارير شاملة للموظفين والحضور والإجازات
- رسوم بيانية تفاعلية
- تصدير PDF و Excel
- فلاتر متقدمة للبيانات

### 6. إعدادات النظام
- إعدادات الشركة والمعلومات الأساسية
- إدارة الأقسام والمسميات الوظيفية
- إدارة أنواع الإجازات
- إدارة المستخدمين والصلاحيات

## المتطلبات التقنية

- PHP 7.4 أو أحدث
- MySQL 5.7 أو أحدث
- Apache/Nginx
- مكتبات PHP: PDO, GD, mbstring

## التثبيت

### 1. تحميل الملفات
```bash
git clone [repository-url]
cd employee-management-system
```

### 2. إعداد قاعدة البيانات
```sql
-- إنشاء قاعدة بيانات جديدة
mysql -u root -p < database/schema.sql

-- أو تحديث قاعدة بيانات موجودة
mysql -u root -p < database/update_schema.sql
```

### 3. إعداد الاتصال
```php
// تحديث ملف config/database.php
$host = 'localhost';
$dbname = 'employee_management';
$username = 'your_username';
$password = 'your_password';
```

### 4. إعداد الصلاحيات
```bash
chmod 755 uploads/
chmod 755 uploads/employees/
chmod 755 uploads/company/
```

### 5. الوصول للنظام
- الرابط: `http://your-domain/`
- المستخدم الافتراضي: `admin`
- كلمة المرور: `password`

## هيكل المشروع

```
employee-management-system/
├── assets/                 # الملفات الثابتة (CSS, JS, Images)
├── config/                 # ملفات الإعداد
├── database/              # ملفات قاعدة البيانات
├── employees/             # وحدة إدارة الموظفين
├── attendance/            # وحدة الحضور والانصراف
├── leaves/                # وحدة إدارة الإجازات
├── notifications/         # وحدة الإشعارات
├── reports/               # وحدة التقارير
├── settings/              # وحدة الإعدادات
├── includes/              # ملفات مشتركة
├── uploads/               # ملفات المستندات المرفوعة
├── ajax/                  # ملفات AJAX
└── index.php             # الصفحة الرئيسية
```

## الاستخدام

### إدارة الموظفين
1. انتقل إلى "إدارة الموظفين"
2. اضغط "إضافة موظف جديد"
3. املأ البيانات المطلوبة
4. ارفع المستندات اللازمة
5. احفظ البيانات

### تسجيل الحضور
1. انتقل إلى "الحضور والانصراف"
2. اختر "تسجيل حضور"
3. اختر الموظف والتاريخ
4. سجل أوقات الحضور والانصراف

### طلب إجازة
1. انتقل إلى "إدارة الإجازات"
2. اضغط "طلب إجازة جديد"
3. اختر نوع الإجازة والتواريخ
4. اكتب السبب وأرسل الطلب

### عرض التقارير
1. انتقل إلى "التقارير"
2. اختر نوع التقرير المطلوب
3. طبق الفلاتر المناسبة
4. اعرض أو صدر التقرير

## الأمان

- تشفير كلمات المرور باستخدام bcrypt
- حماية من SQL Injection
- تنظيف البيانات المدخلة
- نظام صلاحيات متدرج
- جلسات آمنة

## التخصيص

### إضافة حقول جديدة
1. حدث قاعدة البيانات
2. عدل النماذج المناسبة
3. حدث ملفات المعالجة

### تغيير التصميم
1. عدل ملفات CSS في `assets/css/`
2. حدث ملفات JavaScript في `assets/js/`
3. عدل ملفات HTML في المجلدات المناسبة

### إضافة لغات جديدة
1. أنشئ ملفات ترجمة في `includes/lang/`
2. حدث ملف `includes/functions.php`
3. عدل واجهة المستخدم

## الدعم والمساعدة

### المشاكل الشائعة

**خطأ في الاتصال بقاعدة البيانات:**
- تأكد من صحة بيانات الاتصال في `config/database.php`
- تأكد من تشغيل خدمة MySQL

**مشكلة في رفع الملفات:**
- تأكد من صلاحيات مجلد `uploads/`
- تحقق من إعدادات PHP للحد الأقصى لحجم الملف

**مشكلة في عرض الخطوط العربية:**
- تأكد من ترميز UTF-8 في قاعدة البيانات
- تحقق من إعدادات الخط في CSS

### التطوير والمساهمة

1. Fork المشروع
2. أنشئ branch جديد للميزة
3. اكتب الكود مع التوثيق
4. اختبر التغييرات
5. أرسل Pull Request

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - انظر ملف LICENSE للتفاصيل.

## الإصدارات

### الإصدار 1.0.0
- إدارة الموظفين الأساسية
- نظام الحضور والانصراف
- إدارة الإجازات
- التقارير الأساسية
- واجهة Bootstrap عربية

### التحديثات المستقبلية
- تطبيق موبايل
- API للتكامل مع أنظمة أخرى
- نظام الرواتب
- إدارة الأداء
- نظام التدريب

## الاتصال

للاستفسارات والدعم التقني، يرجى التواصل عبر:
- البريد الإلكتروني: <EMAIL>
- الموقع الإلكتروني: www.example.com
