<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit();
}

$page_title = 'طلب إجازة جديدة';
$success_message = '';
$error_message = '';

try {
    $pdo = getConnection();
    
    // الحصول على أنواع الإجازات
    $leave_types_stmt = $pdo->query("SELECT * FROM leave_types WHERE status = 'active' ORDER BY name");
    $leave_types = $leave_types_stmt->fetchAll();
    
    // الحصول على قائمة الموظفين (للمدراء)
    $employees_stmt = $pdo->query("
        SELECT id, employee_number, first_name, last_name 
        FROM employees 
        WHERE status = 'active' 
        ORDER BY first_name, last_name
    ");
    $employees = $employees_stmt->fetchAll();
    
    // الحصول على الموظف الحالي إذا كان موظفاً عادياً
    $current_employee = null;
    if ($_SESSION['role'] == 'employee') {
        $emp_stmt = $pdo->prepare("SELECT * FROM employees WHERE user_id = ?");
        $emp_stmt->execute([$_SESSION['user_id']]);
        $current_employee = $emp_stmt->fetch();
    }
    
} catch (PDOException $e) {
    $leave_types = [];
    $employees = [];
}

// معالجة إرسال طلب الإجازة
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    try {
        $pdo = getConnection();
        
        // التحقق من صحة البيانات
        $required_fields = ['employee_id', 'leave_type_id', 'start_date', 'end_date', 'reason'];
        foreach ($required_fields as $field) {
            if (empty($_POST[$field])) {
                throw new Exception('يرجى ملء جميع الحقول المطلوبة');
            }
        }
        
        $employee_id = (int)$_POST['employee_id'];
        $leave_type_id = (int)$_POST['leave_type_id'];
        $start_date = $_POST['start_date'];
        $end_date = $_POST['end_date'];
        $reason = sanitizeInput($_POST['reason']);
        
        // التحقق من صحة التواريخ
        if (strtotime($start_date) > strtotime($end_date)) {
            throw new Exception('تاريخ البداية يجب أن يكون قبل تاريخ النهاية');
        }
        
        if (strtotime($start_date) < strtotime(date('Y-m-d'))) {
            throw new Exception('لا يمكن طلب إجازة في تاريخ سابق');
        }
        
        // حساب عدد الأيام
        $start = new DateTime($start_date);
        $end = new DateTime($end_date);
        $end->modify('+1 day'); // لتضمين اليوم الأخير
        $interval = $start->diff($end);
        $days_requested = $interval->days;
        
        // التحقق من الرصيد المتاح
        $balance_stmt = $pdo->prepare("
            SELECT * FROM leave_balances 
            WHERE employee_id = ? AND leave_type_id = ? AND year = YEAR(CURDATE())
        ");
        $balance_stmt->execute([$employee_id, $leave_type_id]);
        $balance = $balance_stmt->fetch();
        
        if (!$balance || $balance['remaining_days'] < $days_requested) {
            throw new Exception('الرصيد المتاح غير كافي لهذا النوع من الإجازات');
        }
        
        // التحقق من عدم تداخل الإجازات
        $overlap_stmt = $pdo->prepare("
            SELECT COUNT(*) FROM leave_requests 
            WHERE employee_id = ? 
            AND status IN ('pending', 'approved')
            AND (
                (start_date <= ? AND end_date >= ?) OR
                (start_date <= ? AND end_date >= ?) OR
                (start_date >= ? AND end_date <= ?)
            )
        ");
        $overlap_stmt->execute([
            $employee_id, 
            $start_date, $start_date,
            $end_date, $end_date,
            $start_date, $end_date
        ]);
        
        if ($overlap_stmt->fetchColumn() > 0) {
            throw new Exception('يوجد تداخل مع إجازة أخرى في نفس الفترة');
        }
        
        // إدراج طلب الإجازة
        $insert_stmt = $pdo->prepare("
            INSERT INTO leave_requests (
                employee_id, leave_type_id, start_date, end_date, 
                days_requested, reason, requested_by, status, created_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, 'pending', NOW())
        ");
        
        $insert_stmt->execute([
            $employee_id, $leave_type_id, $start_date, $end_date,
            $days_requested, $reason, $_SESSION['user_id']
        ]);
        
        $request_id = $pdo->lastInsertId();
        
        // إرسال إشعار للمدير
        $manager_stmt = $pdo->prepare("
            SELECT u.id FROM users u 
            JOIN employees e ON u.id = e.user_id 
            WHERE u.role IN ('manager', 'hr') AND u.status = 'active'
            LIMIT 1
        ");
        $manager_stmt->execute();
        $manager = $manager_stmt->fetch();
        
        if ($manager) {
            sendNotification(
                $manager['id'],
                'طلب إجازة جديد',
                "تم تقديم طلب إجازة جديد يتطلب الموافقة",
                'info'
            );
        }
        
        // تسجيل النشاط
        logActivity($_SESSION['user_id'], 'طلب إجازة', "تم تقديم طلب إجازة من $start_date إلى $end_date");
        
        $success_message = 'تم تقديم طلب الإجازة بنجاح وهو في انتظار الموافقة';
        
        // إعادة تعيين النموذج
        $_POST = [];
        
    } catch (Exception $e) {
        $error_message = $e->getMessage();
    }
}

include '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
            <div class="position-sticky pt-3">
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link" href="../index.php">
                            <i class="fas fa-tachometer-alt me-2"></i>
                            لوحة التحكم
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../employees/list.php">
                            <i class="fas fa-users me-2"></i>
                            قائمة الموظفين
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="request.php">
                            <i class="fas fa-paper-plane me-2"></i>
                            طلب إجازة
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="pending.php">
                            <i class="fas fa-hourglass-half me-2"></i>
                            الطلبات المعلقة
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="balance.php">
                            <i class="fas fa-balance-scale me-2"></i>
                            أرصدة الإجازات
                        </a>
                    </li>
                </ul>
            </div>
        </nav>
        
        <!-- Main Content -->
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">
                    <i class="fas fa-paper-plane me-2"></i>
                    طلب إجازة جديدة
                </h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <a href="pending.php" class="btn btn-outline-secondary">
                        <i class="fas fa-list me-1"></i>
                        عرض الطلبات
                    </a>
                </div>
            </div>

            <?php if ($success_message): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle me-2"></i>
                    <?php echo $success_message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?php if ($error_message): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <?php echo $error_message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <div class="row">
                <div class="col-lg-8">
                    <!-- نموذج طلب الإجازة -->
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">
                                <i class="fas fa-calendar-plus me-2"></i>
                                تفاصيل طلب الإجازة
                            </h6>
                        </div>
                        <div class="card-body">
                            <form method="POST" id="leaveRequestForm" class="needs-validation" novalidate>
                                <?php if ($_SESSION['role'] != 'employee' || !$current_employee): ?>
                                <div class="mb-3">
                                    <label for="employee_id" class="form-label">الموظف <span class="text-danger">*</span></label>
                                    <select class="form-select" id="employee_id" name="employee_id" required>
                                        <option value="">اختر الموظف</option>
                                        <?php foreach ($employees as $emp): ?>
                                            <option value="<?php echo $emp['id']; ?>" 
                                                    <?php echo (isset($_POST['employee_id']) && $_POST['employee_id'] == $emp['id']) ? 'selected' : ''; ?>>
                                                <?php echo htmlspecialchars($emp['employee_number'] . ' - ' . $emp['first_name'] . ' ' . $emp['last_name']); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                    <div class="invalid-feedback">يرجى اختيار الموظف</div>
                                </div>
                                <?php else: ?>
                                <input type="hidden" name="employee_id" value="<?php echo $current_employee['id']; ?>">
                                <div class="mb-3">
                                    <label class="form-label">الموظف</label>
                                    <div class="form-control-plaintext">
                                        <?php echo htmlspecialchars($current_employee['employee_number'] . ' - ' . $current_employee['first_name'] . ' ' . $current_employee['last_name']); ?>
                                    </div>
                                </div>
                                <?php endif; ?>
                                
                                <div class="mb-3">
                                    <label for="leave_type_id" class="form-label">نوع الإجازة <span class="text-danger">*</span></label>
                                    <select class="form-select" id="leave_type_id" name="leave_type_id" required>
                                        <option value="">اختر نوع الإجازة</option>
                                        <?php foreach ($leave_types as $type): ?>
                                            <option value="<?php echo $type['id']; ?>" 
                                                    data-days="<?php echo $type['days_per_year']; ?>"
                                                    data-max-consecutive="<?php echo $type['max_consecutive_days']; ?>"
                                                    <?php echo (isset($_POST['leave_type_id']) && $_POST['leave_type_id'] == $type['id']) ? 'selected' : ''; ?>>
                                                <?php echo htmlspecialchars($type['name']); ?>
                                                (<?php echo $type['days_per_year']; ?> يوم سنوياً)
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                    <div class="invalid-feedback">يرجى اختيار نوع الإجازة</div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="start_date" class="form-label">تاريخ البداية <span class="text-danger">*</span></label>
                                        <input type="date" class="form-control" id="start_date" name="start_date" 
                                               value="<?php echo $_POST['start_date'] ?? ''; ?>" 
                                               min="<?php echo date('Y-m-d'); ?>" required>
                                        <div class="invalid-feedback">يرجى إدخال تاريخ البداية</div>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="end_date" class="form-label">تاريخ النهاية <span class="text-danger">*</span></label>
                                        <input type="date" class="form-control" id="end_date" name="end_date" 
                                               value="<?php echo $_POST['end_date'] ?? ''; ?>" 
                                               min="<?php echo date('Y-m-d'); ?>" required>
                                        <div class="invalid-feedback">يرجى إدخال تاريخ النهاية</div>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">عدد الأيام المطلوبة</label>
                                    <div class="form-control-plaintext" id="days_count">
                                        <span class="badge bg-info">سيتم حسابها تلقائياً</span>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="reason" class="form-label">سبب الإجازة <span class="text-danger">*</span></label>
                                    <textarea class="form-control" id="reason" name="reason" rows="4" 
                                              placeholder="اكتب سبب طلب الإجازة..." required><?php echo htmlspecialchars($_POST['reason'] ?? ''); ?></textarea>
                                    <div class="invalid-feedback">يرجى كتابة سبب الإجازة</div>
                                </div>
                                
                                <div class="text-center">
                                    <button type="submit" class="btn btn-primary btn-lg me-3">
                                        <i class="fas fa-paper-plane me-2"></i>
                                        تقديم الطلب
                                    </button>
                                    <button type="reset" class="btn btn-secondary btn-lg">
                                        <i class="fas fa-undo me-2"></i>
                                        إعادة تعيين
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-4">
                    <!-- معلومات الإجازات -->
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">
                                <i class="fas fa-info-circle me-2"></i>
                                معلومات مهمة
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-info">
                                <h6><i class="fas fa-lightbulb me-2"></i>تعليمات:</h6>
                                <ul class="mb-0 small">
                                    <li>يجب تقديم طلب الإجازة قبل التاريخ المطلوب بـ 3 أيام على الأقل</li>
                                    <li>تخضع جميع الطلبات للموافقة من المدير المباشر</li>
                                    <li>يتم خصم أيام الإجازة من الرصيد المتاح</li>
                                    <li>لا يمكن تقديم طلبات متداخلة</li>
                                </ul>
                            </div>
                            
                            <div id="leave_balance_info" class="mt-3" style="display: none;">
                                <h6>الرصيد المتاح:</h6>
                                <div id="balance_details"></div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- أنواع الإجازات -->
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">
                                <i class="fas fa-list me-2"></i>
                                أنواع الإجازات المتاحة
                            </h6>
                        </div>
                        <div class="card-body">
                            <?php foreach ($leave_types as $type): ?>
                            <div class="mb-3 p-3 border rounded">
                                <h6 class="text-primary"><?php echo htmlspecialchars($type['name']); ?></h6>
                                <p class="small text-muted mb-1">
                                    <?php echo htmlspecialchars($type['description']); ?>
                                </p>
                                <div class="row text-center">
                                    <div class="col-6">
                                        <strong><?php echo $type['days_per_year']; ?></strong>
                                        <br><small>يوم سنوياً</small>
                                    </div>
                                    <div class="col-6">
                                        <strong><?php echo $type['max_consecutive_days'] ?: 'غير محدد'; ?></strong>
                                        <br><small>حد أقصى متتالي</small>
                                    </div>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<script>
// حساب عدد الأيام تلقائياً
function calculateDays() {
    const startDate = document.getElementById('start_date').value;
    const endDate = document.getElementById('end_date').value;
    
    if (startDate && endDate) {
        const start = new Date(startDate);
        const end = new Date(endDate);
        
        if (end >= start) {
            const timeDiff = end.getTime() - start.getTime();
            const daysDiff = Math.ceil(timeDiff / (1000 * 3600 * 24)) + 1;
            
            document.getElementById('days_count').innerHTML = 
                `<span class="badge bg-success">${daysDiff} يوم</span>`;
                
            // التحقق من الحد الأقصى
            const leaveType = document.getElementById('leave_type_id');
            if (leaveType.value) {
                const maxDays = leaveType.selectedOptions[0].dataset.maxConsecutive;
                if (maxDays && daysDiff > parseInt(maxDays)) {
                    document.getElementById('days_count').innerHTML += 
                        `<br><small class="text-danger">تجاوز الحد الأقصى المسموح (${maxDays} يوم)</small>`;
                }
            }
        } else {
            document.getElementById('days_count').innerHTML = 
                '<span class="badge bg-danger">تاريخ غير صحيح</span>';
        }
    }
}

// تحديث الرصيد المتاح
function updateLeaveBalance() {
    const employeeId = document.getElementById('employee_id').value;
    const leaveTypeId = document.getElementById('leave_type_id').value;
    
    if (employeeId && leaveTypeId) {
        fetch(`get_leave_balance.php?employee_id=${employeeId}&leave_type_id=${leaveTypeId}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    document.getElementById('balance_details').innerHTML = `
                        <div class="progress mb-2">
                            <div class="progress-bar bg-info" style="width: ${(data.remaining / data.total) * 100}%"></div>
                        </div>
                        <small>المتبقي: ${data.remaining} من ${data.total} يوم</small>
                    `;
                    document.getElementById('leave_balance_info').style.display = 'block';
                } else {
                    document.getElementById('leave_balance_info').style.display = 'none';
                }
            })
            .catch(error => {
                console.error('Error:', error);
            });
    }
}

// ربط الأحداث
document.getElementById('start_date').addEventListener('change', calculateDays);
document.getElementById('end_date').addEventListener('change', calculateDays);
document.getElementById('employee_id').addEventListener('change', updateLeaveBalance);
document.getElementById('leave_type_id').addEventListener('change', updateLeaveBalance);

// تفعيل التحقق من صحة النموذج
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();

// تحديد الحد الأدنى لتاريخ النهاية
document.getElementById('start_date').addEventListener('change', function() {
    document.getElementById('end_date').min = this.value;
});
</script>

<?php include '../includes/footer.php'; ?>
