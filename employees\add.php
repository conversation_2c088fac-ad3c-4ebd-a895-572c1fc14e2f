<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit();
}

$page_title = 'إضافة موظف جديد';
$success_message = '';
$error_message = '';

// الحصول على الأقسام والمناصب
try {
    $pdo = getConnection();
    
    $dept_stmt = $pdo->query("SELECT id, name FROM departments WHERE status = 'active' ORDER BY name");
    $departments = $dept_stmt->fetchAll();
    
    $pos_stmt = $pdo->query("SELECT id, title, department_id FROM positions WHERE status = 'active' ORDER BY title");
    $positions = $pos_stmt->fetchAll();
    
} catch (PDOException $e) {
    $departments = [];
    $positions = [];
}

// معالجة إرسال النموذج
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    try {
        $pdo = getConnection();
        
        // التحقق من صحة البيانات
        $required_fields = ['first_name', 'last_name', 'national_id', 'birth_date', 'hire_date'];
        $missing_fields = [];
        
        foreach ($required_fields as $field) {
            if (empty($_POST[$field])) {
                $missing_fields[] = $field;
            }
        }
        
        if (!empty($missing_fields)) {
            throw new Exception('يرجى ملء جميع الحقول المطلوبة');
        }
        
        // التحقق من عدم تكرار الرقم الوطني
        $check_stmt = $pdo->prepare("SELECT id FROM employees WHERE national_id = ?");
        $check_stmt->execute([$_POST['national_id']]);
        if ($check_stmt->fetch()) {
            throw new Exception('الرقم الوطني موجود مسبقاً');
        }
        
        // إنشاء رقم وظيفي تلقائي
        $emp_number_stmt = $pdo->query("SELECT MAX(CAST(SUBSTRING(employee_number, 4) AS UNSIGNED)) as max_num FROM employees WHERE employee_number LIKE 'EMP%'");
        $max_num = $emp_number_stmt->fetchColumn() ?: 0;
        $employee_number = 'EMP' . str_pad($max_num + 1, 4, '0', STR_PAD_LEFT);
        
        // إدراج الموظف الجديد
        $stmt = $pdo->prepare("
            INSERT INTO employees (
                employee_number, first_name, last_name, national_id, birth_date, 
                city, address, phone, email, identity_number, department_id, 
                position_id, hire_date, contract_start_date, contract_end_date, 
                education_level, salary, emergency_contact_name, emergency_contact_phone, 
                notes, created_at
            ) VALUES (
                ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW()
            )
        ");
        
        $stmt->execute([
            $employee_number,
            sanitizeInput($_POST['first_name']),
            sanitizeInput($_POST['last_name']),
            sanitizeInput($_POST['national_id']),
            $_POST['birth_date'],
            sanitizeInput($_POST['city']),
            sanitizeInput($_POST['address']),
            sanitizeInput($_POST['phone']),
            sanitizeInput($_POST['email']),
            sanitizeInput($_POST['identity_number']),
            !empty($_POST['department_id']) ? $_POST['department_id'] : null,
            !empty($_POST['position_id']) ? $_POST['position_id'] : null,
            $_POST['hire_date'],
            !empty($_POST['contract_start_date']) ? $_POST['contract_start_date'] : null,
            !empty($_POST['contract_end_date']) ? $_POST['contract_end_date'] : null,
            !empty($_POST['education_level']) ? $_POST['education_level'] : null,
            !empty($_POST['salary']) ? $_POST['salary'] : null,
            sanitizeInput($_POST['emergency_contact_name']),
            sanitizeInput($_POST['emergency_contact_phone']),
            sanitizeInput($_POST['notes'])
        ]);
        
        $employee_id = $pdo->lastInsertId();
        
        // تسجيل النشاط
        logActivity($_SESSION['user_id'], 'إضافة موظف جديد', "تم إضافة الموظف: {$_POST['first_name']} {$_POST['last_name']}");
        
        // إنشاء رصيد إجازات للموظف الجديد
        $leave_types_stmt = $pdo->query("SELECT id, days_per_year FROM leave_types WHERE status = 'active'");
        $leave_types = $leave_types_stmt->fetchAll();
        
        foreach ($leave_types as $leave_type) {
            $balance_stmt = $pdo->prepare("
                INSERT INTO leave_balances (employee_id, leave_type_id, year, total_days, remaining_days) 
                VALUES (?, ?, YEAR(CURDATE()), ?, ?)
            ");
            $balance_stmt->execute([
                $employee_id, 
                $leave_type['id'], 
                $leave_type['days_per_year'], 
                $leave_type['days_per_year']
            ]);
        }
        
        $success_message = 'تم إضافة الموظف بنجاح';
        
        // إعادة تعيين النموذج
        $_POST = [];
        
    } catch (Exception $e) {
        $error_message = $e->getMessage();
    }
}

include '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
            <div class="position-sticky pt-3">
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link" href="../index.php">
                            <i class="fas fa-tachometer-alt me-2"></i>
                            لوحة التحكم
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="list.php">
                            <i class="fas fa-users me-2"></i>
                            قائمة الموظفين
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="add.php">
                            <i class="fas fa-plus me-2"></i>
                            إضافة موظف
                        </a>
                    </li>
                </ul>
            </div>
        </nav>
        
        <!-- Main Content -->
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">
                    <i class="fas fa-user-plus me-2"></i>
                    إضافة موظف جديد
                </h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <a href="list.php" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-right me-1"></i>
                        العودة للقائمة
                    </a>
                </div>
            </div>

            <?php if ($success_message): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle me-2"></i>
                    <?php echo $success_message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?php if ($error_message): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <?php echo $error_message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <form method="POST" id="employeeForm" class="needs-validation" novalidate>
                <!-- البيانات الشخصية -->
                <div class="card shadow mb-4 fade-in">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="fas fa-user me-2"></i>
                            البيانات الشخصية
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="first_name" class="form-label">الاسم الأول <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="first_name" name="first_name" 
                                       value="<?php echo htmlspecialchars($_POST['first_name'] ?? ''); ?>" required>
                                <div class="invalid-feedback">يرجى إدخال الاسم الأول</div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="last_name" class="form-label">الاسم الأخير <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="last_name" name="last_name" 
                                       value="<?php echo htmlspecialchars($_POST['last_name'] ?? ''); ?>" required>
                                <div class="invalid-feedback">يرجى إدخال الاسم الأخير</div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="national_id" class="form-label">الرقم الوطني <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="national_id" name="national_id" 
                                       value="<?php echo htmlspecialchars($_POST['national_id'] ?? ''); ?>" 
                                       pattern="[0-9]{10}" maxlength="10" required>
                                <div class="invalid-feedback">يرجى إدخال رقم وطني صحيح (10 أرقام)</div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="birth_date" class="form-label">تاريخ الميلاد <span class="text-danger">*</span></label>
                                <input type="date" class="form-control" id="birth_date" name="birth_date" 
                                       value="<?php echo $_POST['birth_date'] ?? ''; ?>" required>
                                <div class="invalid-feedback">يرجى إدخال تاريخ الميلاد</div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="city" class="form-label">المدينة</label>
                                <input type="text" class="form-control" id="city" name="city" 
                                       value="<?php echo htmlspecialchars($_POST['city'] ?? ''); ?>">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="phone" class="form-label">رقم الهاتف</label>
                                <input type="tel" class="form-control" id="phone" name="phone" 
                                       value="<?php echo htmlspecialchars($_POST['phone'] ?? ''); ?>">
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="email" class="form-label">البريد الإلكتروني</label>
                                <input type="email" class="form-control" id="email" name="email" 
                                       value="<?php echo htmlspecialchars($_POST['email'] ?? ''); ?>">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="identity_number" class="form-label">رقم إثبات الهوية</label>
                                <input type="text" class="form-control" id="identity_number" name="identity_number" 
                                       value="<?php echo htmlspecialchars($_POST['identity_number'] ?? ''); ?>">
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="address" class="form-label">العنوان</label>
                            <textarea class="form-control" id="address" name="address" rows="3"><?php echo htmlspecialchars($_POST['address'] ?? ''); ?></textarea>
                        </div>
                    </div>
                </div>

                <!-- البيانات الوظيفية -->
                <div class="card shadow mb-4 fade-in">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="fas fa-briefcase me-2"></i>
                            البيانات الوظيفية
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="department_id" class="form-label">القسم</label>
                                <select class="form-select" id="department_id" name="department_id">
                                    <option value="">اختر القسم</option>
                                    <?php foreach ($departments as $dept): ?>
                                        <option value="<?php echo $dept['id']; ?>" 
                                                <?php echo (isset($_POST['department_id']) && $_POST['department_id'] == $dept['id']) ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($dept['name']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="position_id" class="form-label">المنصب</label>
                                <select class="form-select" id="position_id" name="position_id">
                                    <option value="">اختر المنصب</option>
                                    <?php foreach ($positions as $pos): ?>
                                        <option value="<?php echo $pos['id']; ?>" 
                                                data-department="<?php echo $pos['department_id']; ?>"
                                                <?php echo (isset($_POST['position_id']) && $_POST['position_id'] == $pos['id']) ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($pos['title']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="hire_date" class="form-label">تاريخ التوظيف <span class="text-danger">*</span></label>
                                <input type="date" class="form-control" id="hire_date" name="hire_date" 
                                       value="<?php echo $_POST['hire_date'] ?? date('Y-m-d'); ?>" required>
                                <div class="invalid-feedback">يرجى إدخال تاريخ التوظيف</div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="contract_start_date" class="form-label">بداية العقد</label>
                                <input type="date" class="form-control" id="contract_start_date" name="contract_start_date" 
                                       value="<?php echo $_POST['contract_start_date'] ?? ''; ?>">
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="contract_end_date" class="form-label">نهاية العقد</label>
                                <input type="date" class="form-control" id="contract_end_date" name="contract_end_date" 
                                       value="<?php echo $_POST['contract_end_date'] ?? ''; ?>">
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="education_level" class="form-label">المؤهل العلمي</label>
                                <select class="form-select" id="education_level" name="education_level">
                                    <option value="">اختر المؤهل</option>
                                    <option value="ثانوية" <?php echo (isset($_POST['education_level']) && $_POST['education_level'] == 'ثانوية') ? 'selected' : ''; ?>>شهادة ثانوية</option>
                                    <option value="دبلوم متوسط" <?php echo (isset($_POST['education_level']) && $_POST['education_level'] == 'دبلوم متوسط') ? 'selected' : ''; ?>>دبلوم متوسط</option>
                                    <option value="دبلوم عالي" <?php echo (isset($_POST['education_level']) && $_POST['education_level'] == 'دبلوم عالي') ? 'selected' : ''; ?>>دبلوم عالي</option>
                                    <option value="بكالوريوس" <?php echo (isset($_POST['education_level']) && $_POST['education_level'] == 'بكالوريوس') ? 'selected' : ''; ?>>بكالوريوس</option>
                                    <option value="ليسانس" <?php echo (isset($_POST['education_level']) && $_POST['education_level'] == 'ليسانس') ? 'selected' : ''; ?>>ليسانس</option>
                                    <option value="ماجستير" <?php echo (isset($_POST['education_level']) && $_POST['education_level'] == 'ماجستير') ? 'selected' : ''; ?>>ماجستير</option>
                                    <option value="دكتوراة" <?php echo (isset($_POST['education_level']) && $_POST['education_level'] == 'دكتوراة') ? 'selected' : ''; ?>>دكتوراة</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="salary" class="form-label">الراتب</label>
                                <div class="input-group">
                                    <input type="number" class="form-control" id="salary" name="salary" 
                                           value="<?php echo $_POST['salary'] ?? ''; ?>" step="0.01" min="0">
                                    <span class="input-group-text">ريال</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- معلومات الطوارئ -->
                <div class="card shadow mb-4 fade-in">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="fas fa-phone me-2"></i>
                            معلومات الطوارئ
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="emergency_contact_name" class="form-label">اسم جهة الاتصال</label>
                                <input type="text" class="form-control" id="emergency_contact_name" name="emergency_contact_name" 
                                       value="<?php echo htmlspecialchars($_POST['emergency_contact_name'] ?? ''); ?>">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="emergency_contact_phone" class="form-label">رقم هاتف الطوارئ</label>
                                <input type="tel" class="form-control" id="emergency_contact_phone" name="emergency_contact_phone" 
                                       value="<?php echo htmlspecialchars($_POST['emergency_contact_phone'] ?? ''); ?>">
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="notes" class="form-label">ملاحظات</label>
                            <textarea class="form-control" id="notes" name="notes" rows="3"><?php echo htmlspecialchars($_POST['notes'] ?? ''); ?></textarea>
                        </div>
                    </div>
                </div>

                <!-- أزرار الحفظ -->
                <div class="card shadow mb-4">
                    <div class="card-body text-center">
                        <button type="submit" class="btn btn-primary btn-lg me-3">
                            <i class="fas fa-save me-2"></i>
                            حفظ البيانات
                        </button>
                        <a href="list.php" class="btn btn-secondary btn-lg">
                            <i class="fas fa-times me-2"></i>
                            إلغاء
                        </a>
                    </div>
                </div>
            </form>
        </main>
    </div>
</div>

<script>
// تفعيل التحقق من صحة النموذج
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();

// تفعيل فلترة المناصب حسب القسم
$('#department_id').on('change', function() {
    const departmentId = $(this).val();
    const positionSelect = $('#position_id');
    
    positionSelect.find('option').each(function() {
        const option = $(this);
        if (option.val() === '') {
            option.show();
        } else if (departmentId === '' || option.data('department') == departmentId) {
            option.show();
        } else {
            option.hide();
        }
    });
    
    positionSelect.val('');
});

// تحديد العمر تلقائياً
$('#birth_date').on('change', function() {
    const birthDate = new Date($(this).val());
    const today = new Date();
    const age = today.getFullYear() - birthDate.getFullYear();
    
    if (age < 18) {
        showError('عمر الموظف يجب أن يكون 18 سنة على الأقل');
        $(this).val('');
    }
});

// تحديد تاريخ نهاية العقد تلقائياً (سنة واحدة من بداية العقد)
$('#contract_start_date').on('change', function() {
    const startDate = new Date($(this).val());
    if (startDate) {
        const endDate = new Date(startDate);
        endDate.setFullYear(endDate.getFullYear() + 1);
        $('#contract_end_date').val(endDate.toISOString().split('T')[0]);
    }
});
</script>

<?php include '../includes/footer.php'; ?>
