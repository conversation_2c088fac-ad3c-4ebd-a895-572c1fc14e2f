<?php
// ملف تهيئة قاعدة البيانات
$page_title = 'تهيئة النظام';

// إعدادات قاعدة البيانات
$db_host = 'localhost';
$db_user = 'root';
$db_pass = '';
$db_name = 'employee_management';

$success_messages = [];
$error_messages = [];

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    try {
        // إنشاء الاتصال بدون تحديد قاعدة البيانات
        $pdo = new PDO("mysql:host=$db_host;charset=utf8mb4", $db_user, $db_pass);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // قراءة ملف SQL
        $sql_file = 'database/schema.sql';
        if (!file_exists($sql_file)) {
            throw new Exception('ملف قاعدة البيانات غير موجود');
        }
        
        $sql_content = file_get_contents($sql_file);
        $sql_statements = explode(';', $sql_content);
        
        // تنفيذ الاستعلامات
        foreach ($sql_statements as $statement) {
            $statement = trim($statement);
            if (!empty($statement)) {
                $pdo->exec($statement);
            }
        }
        
        $success_messages[] = 'تم إنشاء قاعدة البيانات بنجاح';
        $success_messages[] = 'تم إنشاء جميع الجداول بنجاح';
        $success_messages[] = 'تم إدراج البيانات الأساسية بنجاح';
        $success_messages[] = 'يمكنك الآن تسجيل الدخول باستخدام: admin / password';
        
    } catch (Exception $e) {
        $error_messages[] = 'خطأ في تهيئة قاعدة البيانات: ' . $e->getMessage();
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .install-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            overflow: hidden;
            max-width: 600px;
            width: 100%;
        }
        
        .install-header {
            background: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        .install-body {
            padding: 2rem;
        }
        
        .feature-list {
            list-style: none;
            padding: 0;
        }
        
        .feature-list li {
            padding: 0.5rem 0;
            border-bottom: 1px solid #eee;
        }
        
        .feature-list li:last-child {
            border-bottom: none;
        }
        
        .feature-list i {
            color: #28a745;
            margin-left: 10px;
        }
        
        .btn-install {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            color: white;
            font-weight: 600;
            transition: all 0.3s;
        }
        
        .btn-install:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(40, 167, 69, 0.3);
            color: white;
        }
        
        .alert {
            border-radius: 10px;
            border: none;
        }
        
        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="install-card fade-in">
            <div class="install-header">
                <h1><i class="fas fa-cogs me-3"></i>تهيئة نظام إدارة الموظفين</h1>
                <p class="mb-0">مرحباً بك في نظام إدارة الموظفين الشامل</p>
            </div>
            
            <div class="install-body">
                <?php if (!empty($success_messages)): ?>
                    <div class="alert alert-success">
                        <h5><i class="fas fa-check-circle me-2"></i>تم التثبيت بنجاح!</h5>
                        <ul class="mb-0">
                            <?php foreach ($success_messages as $message): ?>
                                <li><?php echo $message; ?></li>
                            <?php endforeach; ?>
                        </ul>
                        <hr>
                        <div class="text-center">
                            <a href="login.php" class="btn btn-success">
                                <i class="fas fa-sign-in-alt me-2"></i>الانتقال لصفحة تسجيل الدخول
                            </a>
                        </div>
                    </div>
                <?php endif; ?>
                
                <?php if (!empty($error_messages)): ?>
                    <div class="alert alert-danger">
                        <h5><i class="fas fa-exclamation-triangle me-2"></i>حدث خطأ!</h5>
                        <ul class="mb-0">
                            <?php foreach ($error_messages as $message): ?>
                                <li><?php echo $message; ?></li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                <?php endif; ?>
                
                <?php if (empty($success_messages)): ?>
                    <div class="text-center mb-4">
                        <h3>مميزات النظام</h3>
                        <p class="text-muted">نظام شامل لإدارة شؤون الموظفين بأحدث التقنيات</p>
                    </div>
                    
                    <ul class="feature-list">
                        <li><i class="fas fa-users"></i> إدارة شاملة للموظفين مع جميع البيانات الشخصية والوظيفية</li>
                        <li><i class="fas fa-clock"></i> نظام حضور وانصراف متطور مع التقارير التفصيلية</li>
                        <li><i class="fas fa-calendar-times"></i> إدارة الإجازات مع نظام موافقة متعدد المستويات</li>
                        <li><i class="fas fa-file-alt"></i> تحميل وإدارة مستندات الموظفين</li>
                        <li><i class="fas fa-bell"></i> نظام إشعارات ذكي للتنبيهات المهمة</li>
                        <li><i class="fas fa-chart-bar"></i> تقارير شاملة مع إمكانية التصدير</li>
                        <li><i class="fas fa-cog"></i> إعدادات مرنة للشركة والأقسام</li>
                        <li><i class="fas fa-mobile-alt"></i> واجهة متجاوبة تعمل على جميع الأجهزة</li>
                    </ul>
                    
                    <div class="text-center mt-4">
                        <form method="POST">
                            <button type="submit" class="btn btn-install btn-lg">
                                <i class="fas fa-download me-2"></i>
                                بدء التثبيت
                            </button>
                        </form>
                        
                        <div class="mt-3">
                            <small class="text-muted">
                                <i class="fas fa-info-circle me-1"></i>
                                سيتم إنشاء قاعدة البيانات وجميع الجداول المطلوبة تلقائياً
                            </small>
                        </div>
                    </div>
                    
                    <div class="mt-4 p-3 bg-light rounded">
                        <h6><i class="fas fa-server me-2"></i>متطلبات النظام:</h6>
                        <ul class="small mb-0">
                            <li>PHP 7.4 أو أحدث</li>
                            <li>MySQL 5.7 أو أحدث</li>
                            <li>Apache أو Nginx</li>
                            <li>مساحة تخزين 100 ميجابايت على الأقل</li>
                        </ul>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
