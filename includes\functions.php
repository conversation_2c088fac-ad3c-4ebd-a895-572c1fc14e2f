<?php
require_once 'config/database.php';

// دالة الحصول على إجمالي عدد الموظفين
function getTotalEmployees() {
    try {
        $pdo = getConnection();
        $stmt = $pdo->query("SELECT COUNT(*) FROM employees WHERE status = 'active'");
        return $stmt->fetchColumn();
    } catch (PDOException $e) {
        return 0;
    }
}

// دالة الحصول على عدد الحاضرين اليوم
function getTodayAttendance() {
    try {
        $pdo = getConnection();
        $today = date('Y-m-d');
        $stmt = $pdo->prepare("SELECT COUNT(DISTINCT employee_id) FROM attendance WHERE DATE(check_in) = ?");
        $stmt->execute([$today]);
        return $stmt->fetchColumn();
    } catch (PDOException $e) {
        return 0;
    }
}

// دالة الحصول على عدد طلبات الإجازة المعلقة
function getPendingLeaveRequests() {
    try {
        $pdo = getConnection();
        $stmt = $pdo->query("SELECT COUNT(*) FROM leave_requests WHERE status = 'pending'");
        return $stmt->fetchColumn();
    } catch (PDOException $e) {
        return 0;
    }
}

// دالة الحصول على عدد العقود المنتهية قريباً
function getExpiringContracts() {
    try {
        $pdo = getConnection();
        $next_month = date('Y-m-d', strtotime('+30 days'));
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM employees WHERE contract_end_date <= ? AND contract_end_date >= CURDATE() AND status = 'active'");
        $stmt->execute([$next_month]);
        return $stmt->fetchColumn();
    } catch (PDOException $e) {
        return 0;
    }
}

// دالة الحصول على الأنشطة الأخيرة
function getRecentActivities() {
    try {
        $pdo = getConnection();
        $stmt = $pdo->query("
            SELECT 
                'تسجيل حضور' as activity,
                CONCAT(e.first_name, ' ', e.last_name) as employee_name,
                a.check_in as date,
                'حضور' as status
            FROM attendance a
            JOIN employees e ON a.employee_id = e.id
            WHERE DATE(a.check_in) = CURDATE()
            ORDER BY a.check_in DESC
            LIMIT 10
        ");
        
        $activities = $stmt->fetchAll();
        $html = '';
        
        foreach ($activities as $activity) {
            $status_class = $activity['status'] == 'حضور' ? 'success' : 'warning';
            $html .= "<tr>
                <td>{$activity['activity']}</td>
                <td>{$activity['employee_name']}</td>
                <td>" . date('Y-m-d H:i', strtotime($activity['date'])) . "</td>
                <td><span class='badge bg-{$status_class}'>{$activity['status']}</span></td>
            </tr>";
        }
        
        return $html ?: '<tr><td colspan="4" class="text-center">لا توجد أنشطة حديثة</td></tr>';
    } catch (PDOException $e) {
        return '<tr><td colspan="4" class="text-center">خطأ في تحميل البيانات</td></tr>';
    }
}

// دالة تنسيق التاريخ
function formatDate($date, $format = 'Y-m-d') {
    return date($format, strtotime($date));
}

// دالة تنسيق الوقت
function formatTime($time) {
    return date('H:i', strtotime($time));
}

// دالة حساب العمر
function calculateAge($birthdate) {
    $today = new DateTime();
    $birth = new DateTime($birthdate);
    $age = $today->diff($birth);
    return $age->y;
}

// دالة حساب سنوات الخدمة
function calculateServiceYears($hire_date) {
    $today = new DateTime();
    $hire = new DateTime($hire_date);
    $service = $today->diff($hire);
    return $service->y;
}

// دالة التحقق من صحة البريد الإلكتروني
function validateEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL);
}

// دالة التحقق من صحة رقم الهاتف
function validatePhone($phone) {
    return preg_match('/^[0-9+\-\s()]+$/', $phone);
}

// دالة تنظيف البيانات
function sanitizeInput($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data);
    return $data;
}

// دالة إنشاء كلمة مرور عشوائية
function generatePassword($length = 8) {
    $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
    $password = '';
    for ($i = 0; $i < $length; $i++) {
        $password .= $characters[rand(0, strlen($characters) - 1)];
    }
    return $password;
}

// دالة تشفير كلمة المرور
function hashPassword($password) {
    return password_hash($password, PASSWORD_DEFAULT);
}

// دالة التحقق من كلمة المرور
function verifyPassword($password, $hash) {
    return password_verify($password, $hash);
}

// دالة رفع الملفات
function uploadFile($file, $upload_dir = 'uploads/') {
    if (!isset($file['error']) || is_array($file['error'])) {
        throw new RuntimeException('Invalid parameters.');
    }

    switch ($file['error']) {
        case UPLOAD_ERR_OK:
            break;
        case UPLOAD_ERR_NO_FILE:
            throw new RuntimeException('No file sent.');
        case UPLOAD_ERR_INI_SIZE:
        case UPLOAD_ERR_FORM_SIZE:
            throw new RuntimeException('Exceeded filesize limit.');
        default:
            throw new RuntimeException('Unknown errors.');
    }

    if ($file['size'] > 5000000) { // 5MB
        throw new RuntimeException('Exceeded filesize limit.');
    }

    $finfo = new finfo(FILEINFO_MIME_TYPE);
    $mime_type = $finfo->file($file['tmp_name']);
    
    $allowed_types = [
        'image/jpeg',
        'image/png',
        'image/gif',
        'application/pdf',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    ];
    
    if (!in_array($mime_type, $allowed_types)) {
        throw new RuntimeException('Invalid file format.');
    }

    $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
    $filename = sprintf('%s.%s', sha1_file($file['tmp_name']), $extension);
    
    if (!move_uploaded_file($file['tmp_name'], $upload_dir . $filename)) {
        throw new RuntimeException('Failed to move uploaded file.');
    }

    return $filename;
}

// دالة إرسال الإشعارات
function sendNotification($user_id, $title, $message, $type = 'info') {
    try {
        $pdo = getConnection();
        $stmt = $pdo->prepare("
            INSERT INTO notifications (user_id, title, message, type, created_at) 
            VALUES (?, ?, ?, ?, NOW())
        ");
        return $stmt->execute([$user_id, $title, $message, $type]);
    } catch (PDOException $e) {
        return false;
    }
}

// دالة الحصول على الإشعارات
function getNotifications($user_id, $limit = 10) {
    try {
        $pdo = getConnection();
        $stmt = $pdo->prepare("
            SELECT * FROM notifications 
            WHERE user_id = ? 
            ORDER BY created_at DESC 
            LIMIT ?
        ");
        $stmt->execute([$user_id, $limit]);
        return $stmt->fetchAll();
    } catch (PDOException $e) {
        return [];
    }
}

// دالة تسجيل العمليات
function logActivity($user_id, $action, $details = '') {
    try {
        $pdo = getConnection();
        $stmt = $pdo->prepare("
            INSERT INTO activity_log (user_id, action, details, ip_address, created_at) 
            VALUES (?, ?, ?, ?, NOW())
        ");
        return $stmt->execute([$user_id, $action, $details, $_SERVER['REMOTE_ADDR']]);
    } catch (PDOException $e) {
        return false;
    }
}

// دالة تصدير البيانات إلى CSV
function exportToCSV($data, $filename) {
    header('Content-Type: text/csv; charset=utf-8');
    header('Content-Disposition: attachment; filename=' . $filename);
    
    $output = fopen('php://output', 'w');
    
    // إضافة BOM للدعم العربي
    fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));
    
    if (!empty($data)) {
        // إضافة العناوين
        fputcsv($output, array_keys($data[0]));
        
        // إضافة البيانات
        foreach ($data as $row) {
            fputcsv($output, $row);
        }
    }
    
    fclose($output);
}

// دالة التحقق من الصلاحيات
function checkPermission($permission) {
    if (!isset($_SESSION['permissions'])) {
        return false;
    }
    return in_array($permission, $_SESSION['permissions']);
}

// دالة إعادة التوجيه
function redirect($url) {
    header("Location: $url");
    exit();
}

// دالة عرض الرسائل
function displayMessage($type, $message) {
    $alert_class = [
        'success' => 'alert-success',
        'error' => 'alert-danger',
        'warning' => 'alert-warning',
        'info' => 'alert-info'
    ];
    
    $class = isset($alert_class[$type]) ? $alert_class[$type] : 'alert-info';
    
    return "<div class='alert $class alert-dismissible fade show' role='alert'>
                $message
                <button type='button' class='btn-close' data-bs-dismiss='alert'></button>
            </div>";
}
?>
