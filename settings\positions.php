<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// التحقق من تسجيل الدخول والصلاحيات
if (!isset($_SESSION['user_id']) || !in_array($_SESSION['role'], ['admin', 'hr'])) {
    header('Location: ../login.php');
    exit();
}

$page_title = 'المسميات الوظيفية';
$success_message = '';
$error_message = '';

try {
    $pdo = getConnection();
    
    // الحصول على قائمة المسميات الوظيفية
    $positions_stmt = $pdo->query("
        SELECT 
            p.*,
            d.name as department_name,
            COUNT(e.id) as employee_count
        FROM positions p
        LEFT JOIN departments d ON p.department_id = d.id
        LEFT JOIN employees e ON p.id = e.position_id AND e.status = 'active'
        GROUP BY p.id
        ORDER BY d.name, p.title
    ");
    $positions = $positions_stmt->fetchAll();
    
    // الحصول على قائمة الأقسام للاختيار
    $departments_stmt = $pdo->query("
        SELECT id, name FROM departments 
        WHERE status = 'active' 
        ORDER BY name
    ");
    $departments = $departments_stmt->fetchAll();
    
} catch (PDOException $e) {
    $positions = [];
    $departments = [];
}

// معالجة إضافة/تعديل منصب
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    try {
        $pdo = getConnection();
        
        $action = $_POST['action'];
        $title = sanitizeInput($_POST['title']);
        $description = sanitizeInput($_POST['description']);
        $department_id = $_POST['department_id'] ? (int)$_POST['department_id'] : null;
        $min_salary = $_POST['min_salary'] ? (float)$_POST['min_salary'] : null;
        $max_salary = $_POST['max_salary'] ? (float)$_POST['max_salary'] : null;
        $requirements = sanitizeInput($_POST['requirements']);
        $status = $_POST['status'];
        
        if (empty($title)) {
            throw new Exception('يرجى إدخال المسمى الوظيفي');
        }
        
        // التحقق من صحة الراتب
        if ($min_salary && $max_salary && $min_salary > $max_salary) {
            throw new Exception('الحد الأدنى للراتب لا يمكن أن يكون أكبر من الحد الأقصى');
        }
        
        if ($action == 'add') {
            // التحقق من عدم تكرار المسمى في نفس القسم
            $check_stmt = $pdo->prepare("
                SELECT COUNT(*) FROM positions 
                WHERE title = ? AND (department_id = ? OR (department_id IS NULL AND ? IS NULL))
            ");
            $check_stmt->execute([$title, $department_id, $department_id]);
            if ($check_stmt->fetchColumn() > 0) {
                throw new Exception('المسمى الوظيفي موجود مسبقاً في هذا القسم');
            }
            
            // إضافة منصب جديد
            $insert_stmt = $pdo->prepare("
                INSERT INTO positions (title, description, department_id, min_salary, max_salary, requirements, status, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, NOW())
            ");
            $insert_stmt->execute([$title, $description, $department_id, $min_salary, $max_salary, $requirements, $status]);
            
            logActivity($_SESSION['user_id'], 'إضافة منصب', "تم إضافة المنصب: $title");
            $success_message = 'تم إضافة المنصب بنجاح';
            
        } elseif ($action == 'edit') {
            $position_id = (int)$_POST['position_id'];
            
            // التحقق من عدم تكرار المسمى (باستثناء المنصب الحالي)
            $check_stmt = $pdo->prepare("
                SELECT COUNT(*) FROM positions 
                WHERE title = ? AND (department_id = ? OR (department_id IS NULL AND ? IS NULL)) AND id != ?
            ");
            $check_stmt->execute([$title, $department_id, $department_id, $position_id]);
            if ($check_stmt->fetchColumn() > 0) {
                throw new Exception('المسمى الوظيفي موجود مسبقاً في هذا القسم');
            }
            
            // تحديث المنصب
            $update_stmt = $pdo->prepare("
                UPDATE positions 
                SET title = ?, description = ?, department_id = ?, min_salary = ?, max_salary = ?, 
                    requirements = ?, status = ?, updated_at = NOW()
                WHERE id = ?
            ");
            $update_stmt->execute([$title, $description, $department_id, $min_salary, $max_salary, $requirements, $status, $position_id]);
            
            logActivity($_SESSION['user_id'], 'تعديل منصب', "تم تعديل المنصب: $title");
            $success_message = 'تم تحديث المنصب بنجاح';
        }
        
        // إعادة تحميل المناصب
        $positions_stmt = $pdo->query("
            SELECT 
                p.*,
                d.name as department_name,
                COUNT(e.id) as employee_count
            FROM positions p
            LEFT JOIN departments d ON p.department_id = d.id
            LEFT JOIN employees e ON p.id = e.position_id AND e.status = 'active'
            GROUP BY p.id
            ORDER BY d.name, p.title
        ");
        $positions = $positions_stmt->fetchAll();
        
    } catch (Exception $e) {
        $error_message = $e->getMessage();
    }
}

// معالجة حذف منصب
if (isset($_GET['delete']) && is_numeric($_GET['delete'])) {
    try {
        $pdo = getConnection();
        $position_id = (int)$_GET['delete'];
        
        // التحقق من وجود موظفين في المنصب
        $check_stmt = $pdo->prepare("SELECT COUNT(*) FROM employees WHERE position_id = ?");
        $check_stmt->execute([$position_id]);
        $employee_count = $check_stmt->fetchColumn();
        
        if ($employee_count > 0) {
            throw new Exception('لا يمكن حذف المنصب لوجود موظفين مرتبطين به');
        }
        
        // حذف المنصب
        $delete_stmt = $pdo->prepare("DELETE FROM positions WHERE id = ?");
        $delete_stmt->execute([$position_id]);
        
        logActivity($_SESSION['user_id'], 'حذف منصب', "تم حذف منصب برقم: $position_id");
        $success_message = 'تم حذف المنصب بنجاح';
        
        // إعادة توجيه لتجنب إعادة الحذف
        header('Location: positions.php?success=' . urlencode($success_message));
        exit();
        
    } catch (Exception $e) {
        $error_message = $e->getMessage();
    }
}

// رسالة النجاح من إعادة التوجيه
if (isset($_GET['success'])) {
    $success_message = $_GET['success'];
}

include '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
            <div class="position-sticky pt-3">
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link" href="../index.php">
                            <i class="fas fa-tachometer-alt me-2"></i>
                            لوحة التحكم
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="company.php">
                            <i class="fas fa-building me-2"></i>
                            إعدادات الشركة
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="departments.php">
                            <i class="fas fa-sitemap me-2"></i>
                            إدارة الأقسام
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="positions.php">
                            <i class="fas fa-user-tie me-2"></i>
                            المسميات الوظيفية
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="leave_types.php">
                            <i class="fas fa-calendar-alt me-2"></i>
                            أنواع الإجازات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="users.php">
                            <i class="fas fa-users-cog me-2"></i>
                            إدارة المستخدمين
                        </a>
                    </li>
                </ul>
            </div>
        </nav>
        
        <!-- Main Content -->
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">
                    <i class="fas fa-user-tie me-2"></i>
                    المسميات الوظيفية
                </h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#positionModal" onclick="openAddModal()">
                        <i class="fas fa-plus me-1"></i>
                        إضافة منصب جديد
                    </button>
                </div>
            </div>

            <?php if ($success_message): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle me-2"></i>
                    <?php echo $success_message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?php if ($error_message): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <?php echo $error_message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <!-- قائمة المناصب -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-list me-2"></i>
                        قائمة المناصب (<?php echo count($positions); ?>)
                    </h6>
                </div>
                <div class="card-body">
                    <?php if (empty($positions)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-user-tie fa-3x text-muted mb-3"></i>
                            <h5>لا توجد مناصب</h5>
                            <p class="text-muted">ابدأ بإضافة أول منصب في الشركة</p>
                            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#positionModal" onclick="openAddModal()">
                                <i class="fas fa-plus me-1"></i>
                                إضافة منصب جديد
                            </button>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-bordered data-table" id="positionsTable" width="100%" cellspacing="0">
                                <thead>
                                    <tr>
                                        <th>المسمى الوظيفي</th>
                                        <th>القسم</th>
                                        <th>الوصف</th>
                                        <th>نطاق الراتب</th>
                                        <th>عدد الموظفين</th>
                                        <th>الحالة</th>
                                        <th>تاريخ الإنشاء</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($positions as $position): ?>
                                    <tr>
                                        <td>
                                            <strong><?php echo htmlspecialchars($position['title']); ?></strong>
                                        </td>
                                        <td>
                                            <?php if ($position['department_name']): ?>
                                                <span class="badge bg-secondary">
                                                    <?php echo htmlspecialchars($position['department_name']); ?>
                                                </span>
                                            <?php else: ?>
                                                <span class="text-muted">عام</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if ($position['description']): ?>
                                                <span title="<?php echo htmlspecialchars($position['description']); ?>">
                                                    <?php echo htmlspecialchars(substr($position['description'], 0, 50)) . (strlen($position['description']) > 50 ? '...' : ''); ?>
                                                </span>
                                            <?php else: ?>
                                                <span class="text-muted">لا يوجد وصف</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if ($position['min_salary'] || $position['max_salary']): ?>
                                                <small class="text-muted">
                                                    <?php if ($position['min_salary']): ?>
                                                        <?php echo number_format($position['min_salary']); ?>
                                                    <?php endif; ?>
                                                    <?php if ($position['min_salary'] && $position['max_salary']): ?>
                                                        -
                                                    <?php endif; ?>
                                                    <?php if ($position['max_salary']): ?>
                                                        <?php echo number_format($position['max_salary']); ?>
                                                    <?php endif; ?>
                                                    ريال
                                                </small>
                                            <?php else: ?>
                                                <span class="text-muted">غير محدد</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <span class="badge bg-info">
                                                <?php echo $position['employee_count']; ?> موظف
                                            </span>
                                        </td>
                                        <td>
                                            <?php
                                            $status_classes = [
                                                'active' => 'success',
                                                'inactive' => 'secondary'
                                            ];
                                            $status_text = [
                                                'active' => 'نشط',
                                                'inactive' => 'غير نشط'
                                            ];
                                            $status_class = $status_classes[$position['status']] ?? 'secondary';
                                            ?>
                                            <span class="badge bg-<?php echo $status_class; ?>">
                                                <?php echo $status_text[$position['status']] ?? $position['status']; ?>
                                            </span>
                                        </td>
                                        <td><?php echo formatDate($position['created_at']); ?></td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <button type="button" class="btn btn-sm btn-outline-primary" 
                                                        onclick="openEditModal(<?php echo htmlspecialchars(json_encode($position)); ?>)">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <?php if ($position['employee_count'] == 0): ?>
                                                <button type="button" class="btn btn-sm btn-outline-danger" 
                                                        onclick="confirmDelete(<?php echo $position['id']; ?>, '<?php echo htmlspecialchars($position['title']); ?>')">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                                <?php else: ?>
                                                <button type="button" class="btn btn-sm btn-outline-secondary" disabled 
                                                        title="لا يمكن حذف المنصب لوجود موظفين">
                                                    <i class="fas fa-lock"></i>
                                                </button>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </main>
    </div>
</div>

<!-- Modal إضافة/تعديل منصب -->
<div class="modal fade" id="positionModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="positionModalTitle">إضافة منصب جديد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" id="positionForm" class="needs-validation" novalidate>
                <div class="modal-body">
                    <input type="hidden" name="action" id="modal_action" value="add">
                    <input type="hidden" name="position_id" id="modal_position_id">
                    
                    <div class="row">
                        <div class="col-md-8 mb-3">
                            <label for="modal_title" class="form-label">المسمى الوظيفي <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="modal_title" name="title" required>
                            <div class="invalid-feedback">يرجى إدخال المسمى الوظيفي</div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="modal_department_id" class="form-label">القسم</label>
                            <select class="form-select" id="modal_department_id" name="department_id">
                                <option value="">عام (جميع الأقسام)</option>
                                <?php foreach ($departments as $dept): ?>
                                    <option value="<?php echo $dept['id']; ?>">
                                        <?php echo htmlspecialchars($dept['name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="modal_description" class="form-label">وصف المنصب</label>
                        <textarea class="form-control" id="modal_description" name="description" rows="3" 
                                  placeholder="وصف مختصر عن المنصب ومسؤولياته..."></textarea>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="modal_min_salary" class="form-label">الحد الأدنى للراتب</label>
                            <div class="input-group">
                                <input type="number" class="form-control" id="modal_min_salary" name="min_salary" 
                                       min="0" step="100" placeholder="0">
                                <span class="input-group-text">ريال</span>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="modal_max_salary" class="form-label">الحد الأقصى للراتب</label>
                            <div class="input-group">
                                <input type="number" class="form-control" id="modal_max_salary" name="max_salary" 
                                       min="0" step="100" placeholder="0">
                                <span class="input-group-text">ريال</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="modal_requirements" class="form-label">المتطلبات والمؤهلات</label>
                        <textarea class="form-control" id="modal_requirements" name="requirements" rows="3" 
                                  placeholder="المؤهلات والخبرات المطلوبة للمنصب..."></textarea>
                    </div>
                    
                    <div class="mb-3">
                        <label for="modal_status" class="form-label">الحالة</label>
                        <select class="form-select" id="modal_status" name="status">
                            <option value="active">نشط</option>
                            <option value="inactive">غير نشط</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary" id="modal_submit_btn">
                        <i class="fas fa-save me-1"></i>
                        حفظ
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// فتح modal للإضافة
function openAddModal() {
    document.getElementById('positionModalTitle').textContent = 'إضافة منصب جديد';
    document.getElementById('modal_action').value = 'add';
    document.getElementById('modal_submit_btn').innerHTML = '<i class="fas fa-save me-1"></i>إضافة';
    
    // إعادة تعيين النموذج
    document.getElementById('positionForm').reset();
    document.getElementById('positionForm').classList.remove('was-validated');
}

// فتح modal للتعديل
function openEditModal(position) {
    document.getElementById('positionModalTitle').textContent = 'تعديل المنصب';
    document.getElementById('modal_action').value = 'edit';
    document.getElementById('modal_submit_btn').innerHTML = '<i class="fas fa-save me-1"></i>تحديث';
    
    // ملء البيانات
    document.getElementById('modal_position_id').value = position.id;
    document.getElementById('modal_title').value = position.title;
    document.getElementById('modal_department_id').value = position.department_id || '';
    document.getElementById('modal_description').value = position.description || '';
    document.getElementById('modal_min_salary').value = position.min_salary || '';
    document.getElementById('modal_max_salary').value = position.max_salary || '';
    document.getElementById('modal_requirements').value = position.requirements || '';
    document.getElementById('modal_status').value = position.status;
    
    // إزالة validation classes
    document.getElementById('positionForm').classList.remove('was-validated');
    
    // عرض المودال
    new bootstrap.Modal(document.getElementById('positionModal')).show();
}

// تأكيد الحذف
function confirmDelete(positionId, positionTitle) {
    Swal.fire({
        title: 'تأكيد الحذف',
        text: `هل تريد حذف المنصب: ${positionTitle}؟`,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#6c757d',
        confirmButtonText: 'نعم، احذف',
        cancelButtonText: 'إلغاء'
    }).then((result) => {
        if (result.isConfirmed) {
            window.location.href = `positions.php?delete=${positionId}`;
        }
    });
}

// التحقق من صحة نطاق الراتب
document.getElementById('modal_min_salary').addEventListener('input', validateSalaryRange);
document.getElementById('modal_max_salary').addEventListener('input', validateSalaryRange);

function validateSalaryRange() {
    const minSalary = parseFloat(document.getElementById('modal_min_salary').value) || 0;
    const maxSalary = parseFloat(document.getElementById('modal_max_salary').value) || 0;
    
    if (minSalary > 0 && maxSalary > 0 && minSalary > maxSalary) {
        document.getElementById('modal_max_salary').setCustomValidity('الحد الأقصى يجب أن يكون أكبر من الحد الأدنى');
    } else {
        document.getElementById('modal_max_salary').setCustomValidity('');
    }
}

// تفعيل التحقق من صحة النموذج
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();

// تهيئة DataTable
$(document).ready(function() {
    $('#positionsTable').DataTable({
        language: {
            url: 'https://cdn.datatables.net/plug-ins/1.13.6/i18n/ar.json'
        },
        responsive: true,
        pageLength: 25,
        order: [[0, 'asc']],
        columnDefs: [
            { orderable: false, targets: [7] } // عمود الإجراءات غير قابل للترتيب
        ]
    });
});
</script>

<?php include '../includes/footer.php'; ?>
