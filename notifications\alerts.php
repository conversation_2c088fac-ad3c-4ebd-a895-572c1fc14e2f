<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// التحقق من تسجيل الدخول والصلاحيات
if (!isset($_SESSION['user_id']) || !in_array($_SESSION['role'], ['manager', 'hr', 'admin'])) {
    header('Location: ../login.php');
    exit();
}

$page_title = 'التنبيهات التلقائية';

try {
    $pdo = getConnection();
    
    // العقود المنتهية قريباً (30 يوم)
    $contracts_stmt = $pdo->query("
        SELECT 
            e.id, e.employee_number, e.first_name, e.last_name, 
            e.contract_start_date, e.contract_end_date,
            d.name as department_name,
            DATEDIFF(e.contract_end_date, CURDATE()) as days_remaining
        FROM employees e
        LEFT JOIN departments d ON e.department_id = d.id
        WHERE e.status = 'active' 
        AND e.contract_end_date IS NOT NULL
        AND e.contract_end_date BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 30 DAY)
        ORDER BY e.contract_end_date ASC
    ");
    $expiring_contracts = $contracts_stmt->fetchAll();
    
    // الإقامات المنتهية قريباً (30 يوم)
    $residences_stmt = $pdo->query("
        SELECT 
            e.id, e.employee_number, e.first_name, e.last_name, 
            e.residence_expiry, e.residence_number,
            d.name as department_name,
            DATEDIFF(e.residence_expiry, CURDATE()) as days_remaining
        FROM employees e
        LEFT JOIN departments d ON e.department_id = d.id
        WHERE e.status = 'active' 
        AND e.residence_expiry IS NOT NULL
        AND e.residence_expiry BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 30 DAY)
        ORDER BY e.residence_expiry ASC
    ");
    $expiring_residences = $residences_stmt->fetchAll();
    
    // الإجازات المنتهية قريباً (7 أيام)
    $leaves_stmt = $pdo->query("
        SELECT 
            lr.*, e.employee_number, e.first_name, e.last_name, 
            lt.name as leave_type_name, lt.color as leave_type_color,
            d.name as department_name,
            DATEDIFF(lr.end_date, CURDATE()) as days_remaining
        FROM leave_requests lr
        JOIN employees e ON lr.employee_id = e.id
        JOIN leave_types lt ON lr.leave_type_id = lt.id
        LEFT JOIN departments d ON e.department_id = d.id
        WHERE lr.status = 'approved' 
        AND lr.end_date BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 7 DAY)
        ORDER BY lr.end_date ASC
    ");
    $ending_leaves = $leaves_stmt->fetchAll();
    
    // الموظفون الذين لم يسجلوا حضور اليوم
    $absent_today_stmt = $pdo->query("
        SELECT 
            e.id, e.employee_number, e.first_name, e.last_name,
            d.name as department_name
        FROM employees e
        LEFT JOIN departments d ON e.department_id = d.id
        LEFT JOIN attendance a ON e.id = a.employee_id AND a.date = CURDATE()
        WHERE e.status = 'active' 
        AND a.id IS NULL
        AND CURTIME() > '09:00:00'
        ORDER BY e.first_name, e.last_name
    ");
    $absent_today = $absent_today_stmt->fetchAll();
    
    // الموظفون المتأخرون اليوم
    $late_today_stmt = $pdo->query("
        SELECT 
            a.*, e.employee_number, e.first_name, e.last_name,
            d.name as department_name
        FROM attendance a
        JOIN employees e ON a.employee_id = e.id
        LEFT JOIN departments d ON e.department_id = d.id
        WHERE a.date = CURDATE() 
        AND a.late_minutes > 0
        ORDER BY a.late_minutes DESC
    ");
    $late_today = $late_today_stmt->fetchAll();
    
    // أعياد الميلاد هذا الشهر
    $birthdays_stmt = $pdo->query("
        SELECT 
            e.id, e.employee_number, e.first_name, e.last_name, 
            e.birth_date, d.name as department_name,
            DAY(e.birth_date) as birth_day,
            CASE 
                WHEN DAY(e.birth_date) = DAY(CURDATE()) THEN 0
                WHEN DAY(e.birth_date) > DAY(CURDATE()) THEN DAY(e.birth_date) - DAY(CURDATE())
                ELSE (DAY(LAST_DAY(CURDATE())) - DAY(CURDATE())) + DAY(e.birth_date)
            END as days_until_birthday
        FROM employees e
        LEFT JOIN departments d ON e.department_id = d.id
        WHERE e.status = 'active' 
        AND e.birth_date IS NOT NULL
        AND MONTH(e.birth_date) = MONTH(CURDATE())
        ORDER BY days_until_birthday ASC, e.first_name
    ");
    $birthdays = $birthdays_stmt->fetchAll();
    
} catch (PDOException $e) {
    $expiring_contracts = [];
    $expiring_residences = [];
    $ending_leaves = [];
    $absent_today = [];
    $late_today = [];
    $birthdays = [];
}

include '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
            <div class="position-sticky pt-3">
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link" href="../index.php">
                            <i class="fas fa-tachometer-alt me-2"></i>
                            لوحة التحكم
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../employees/list.php">
                            <i class="fas fa-users me-2"></i>
                            قائمة الموظفين
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="list.php">
                            <i class="fas fa-bell me-2"></i>
                            الإشعارات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="alerts.php">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            التنبيهات التلقائية
                        </a>
                    </li>
                </ul>
            </div>
        </nav>
        
        <!-- Main Content -->
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    التنبيهات التلقائية
                </h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <button type="button" class="btn btn-outline-primary" onclick="refreshAlerts()">
                        <i class="fas fa-sync-alt me-1"></i>
                        تحديث
                    </button>
                </div>
            </div>

            <!-- إحصائيات سريعة -->
            <div class="row mb-4">
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card stats-card border-0 shadow-sm border-left-warning">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                        عقود تنتهي قريباً
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        <?php echo count($expiring_contracts); ?>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-file-contract fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card stats-card border-0 shadow-sm border-left-danger">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                                        إقامات تنتهي قريباً
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        <?php echo count($expiring_residences); ?>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-id-card fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card stats-card border-0 shadow-sm border-left-info">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                        غائبون اليوم
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        <?php echo count($absent_today); ?>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-user-times fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card stats-card border-0 shadow-sm border-left-success">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                        أعياد ميلاد هذا الشهر
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        <?php echo count($birthdays); ?>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-birthday-cake fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <!-- العقود المنتهية قريباً -->
                <div class="col-lg-6 mb-4" id="contracts">
                    <div class="card shadow">
                        <div class="card-header py-3 bg-warning text-dark">
                            <h6 class="m-0 font-weight-bold">
                                <i class="fas fa-file-contract me-2"></i>
                                عقود تنتهي خلال 30 يوم
                                <span class="badge bg-dark"><?php echo count($expiring_contracts); ?></span>
                            </h6>
                        </div>
                        <div class="card-body">
                            <?php if (empty($expiring_contracts)): ?>
                                <div class="text-center py-3">
                                    <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                                    <p class="text-muted">لا توجد عقود تنتهي قريباً</p>
                                </div>
                            <?php else: ?>
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>الموظف</th>
                                                <th>القسم</th>
                                                <th>تاريخ الانتهاء</th>
                                                <th>المتبقي</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($expiring_contracts as $contract): ?>
                                            <tr>
                                                <td>
                                                    <strong><?php echo htmlspecialchars($contract['first_name'] . ' ' . $contract['last_name']); ?></strong>
                                                    <br><small class="text-muted"><?php echo htmlspecialchars($contract['employee_number']); ?></small>
                                                </td>
                                                <td><?php echo htmlspecialchars($contract['department_name'] ?: 'غير محدد'); ?></td>
                                                <td><?php echo formatDate($contract['contract_end_date']); ?></td>
                                                <td>
                                                    <?php
                                                    $badge_class = $contract['days_remaining'] <= 7 ? 'bg-danger' : 
                                                                  ($contract['days_remaining'] <= 15 ? 'bg-warning' : 'bg-info');
                                                    ?>
                                                    <span class="badge <?php echo $badge_class; ?>">
                                                        <?php echo $contract['days_remaining']; ?> يوم
                                                    </span>
                                                </td>
                                            </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- الإقامات المنتهية قريباً -->
                <div class="col-lg-6 mb-4" id="residences">
                    <div class="card shadow">
                        <div class="card-header py-3 bg-danger text-white">
                            <h6 class="m-0 font-weight-bold">
                                <i class="fas fa-id-card me-2"></i>
                                إقامات تنتهي خلال 30 يوم
                                <span class="badge bg-light text-dark"><?php echo count($expiring_residences); ?></span>
                            </h6>
                        </div>
                        <div class="card-body">
                            <?php if (empty($expiring_residences)): ?>
                                <div class="text-center py-3">
                                    <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                                    <p class="text-muted">لا توجد إقامات تنتهي قريباً</p>
                                </div>
                            <?php else: ?>
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>الموظف</th>
                                                <th>القسم</th>
                                                <th>تاريخ الانتهاء</th>
                                                <th>المتبقي</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($expiring_residences as $residence): ?>
                                            <tr>
                                                <td>
                                                    <strong><?php echo htmlspecialchars($residence['first_name'] . ' ' . $residence['last_name']); ?></strong>
                                                    <br><small class="text-muted"><?php echo htmlspecialchars($residence['employee_number']); ?></small>
                                                </td>
                                                <td><?php echo htmlspecialchars($residence['department_name'] ?: 'غير محدد'); ?></td>
                                                <td><?php echo formatDate($residence['residence_expiry']); ?></td>
                                                <td>
                                                    <?php
                                                    $badge_class = $residence['days_remaining'] <= 7 ? 'bg-danger' : 
                                                                  ($residence['days_remaining'] <= 15 ? 'bg-warning' : 'bg-info');
                                                    ?>
                                                    <span class="badge <?php echo $badge_class; ?>">
                                                        <?php echo $residence['days_remaining']; ?> يوم
                                                    </span>
                                                </td>
                                            </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <!-- الغائبون اليوم -->
                <div class="col-lg-6 mb-4">
                    <div class="card shadow">
                        <div class="card-header py-3 bg-info text-white">
                            <h6 class="m-0 font-weight-bold">
                                <i class="fas fa-user-times me-2"></i>
                                لم يسجلوا حضور اليوم
                                <span class="badge bg-light text-dark"><?php echo count($absent_today); ?></span>
                            </h6>
                        </div>
                        <div class="card-body">
                            <?php if (empty($absent_today)): ?>
                                <div class="text-center py-3">
                                    <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                                    <p class="text-muted">جميع الموظفين سجلوا حضورهم</p>
                                </div>
                            <?php else: ?>
                                <div class="list-group list-group-flush">
                                    <?php foreach ($absent_today as $absent): ?>
                                    <div class="list-group-item d-flex justify-content-between align-items-center">
                                        <div>
                                            <strong><?php echo htmlspecialchars($absent['first_name'] . ' ' . $absent['last_name']); ?></strong>
                                            <br><small class="text-muted">
                                                <?php echo htmlspecialchars($absent['employee_number']); ?>
                                                <?php if ($absent['department_name']): ?>
                                                    - <?php echo htmlspecialchars($absent['department_name']); ?>
                                                <?php endif; ?>
                                            </small>
                                        </div>
                                        <span class="badge bg-warning">غائب</span>
                                    </div>
                                    <?php endforeach; ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- أعياد الميلاد -->
                <div class="col-lg-6 mb-4">
                    <div class="card shadow">
                        <div class="card-header py-3 bg-success text-white">
                            <h6 class="m-0 font-weight-bold">
                                <i class="fas fa-birthday-cake me-2"></i>
                                أعياد ميلاد هذا الشهر
                                <span class="badge bg-light text-dark"><?php echo count($birthdays); ?></span>
                            </h6>
                        </div>
                        <div class="card-body">
                            <?php if (empty($birthdays)): ?>
                                <div class="text-center py-3">
                                    <i class="fas fa-calendar fa-2x text-muted mb-2"></i>
                                    <p class="text-muted">لا توجد أعياد ميلاد هذا الشهر</p>
                                </div>
                            <?php else: ?>
                                <div class="list-group list-group-flush">
                                    <?php foreach ($birthdays as $birthday): ?>
                                    <div class="list-group-item d-flex justify-content-between align-items-center">
                                        <div>
                                            <strong><?php echo htmlspecialchars($birthday['first_name'] . ' ' . $birthday['last_name']); ?></strong>
                                            <br><small class="text-muted">
                                                <?php echo htmlspecialchars($birthday['employee_number']); ?>
                                                <?php if ($birthday['department_name']): ?>
                                                    - <?php echo htmlspecialchars($birthday['department_name']); ?>
                                                <?php endif; ?>
                                            </small>
                                        </div>
                                        <div class="text-center">
                                            <?php if ($birthday['days_until_birthday'] == 0): ?>
                                                <span class="badge bg-warning">اليوم!</span>
                                            <?php else: ?>
                                                <span class="badge bg-info">
                                                    بعد <?php echo $birthday['days_until_birthday']; ?> يوم
                                                </span>
                                            <?php endif; ?>
                                            <br><small class="text-muted"><?php echo $birthday['birth_day']; ?> من الشهر</small>
                                        </div>
                                    </div>
                                    <?php endforeach; ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- الإجازات المنتهية قريباً -->
            <?php if (!empty($ending_leaves)): ?>
            <div class="row">
                <div class="col-12 mb-4" id="leaves">
                    <div class="card shadow">
                        <div class="card-header py-3 bg-primary text-white">
                            <h6 class="m-0 font-weight-bold">
                                <i class="fas fa-calendar-times me-2"></i>
                                إجازات تنتهي خلال 7 أيام
                                <span class="badge bg-light text-dark"><?php echo count($ending_leaves); ?></span>
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>الموظف</th>
                                            <th>نوع الإجازة</th>
                                            <th>من تاريخ</th>
                                            <th>إلى تاريخ</th>
                                            <th>المتبقي</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($ending_leaves as $leave): ?>
                                        <tr>
                                            <td>
                                                <strong><?php echo htmlspecialchars($leave['first_name'] . ' ' . $leave['last_name']); ?></strong>
                                                <br><small class="text-muted"><?php echo htmlspecialchars($leave['employee_number']); ?></small>
                                            </td>
                                            <td>
                                                <span class="badge" style="background-color: <?php echo $leave['leave_type_color'] ?: '#6c757d'; ?>">
                                                    <?php echo htmlspecialchars($leave['leave_type_name']); ?>
                                                </span>
                                            </td>
                                            <td><?php echo formatDate($leave['start_date']); ?></td>
                                            <td><?php echo formatDate($leave['end_date']); ?></td>
                                            <td>
                                                <?php
                                                $badge_class = $leave['days_remaining'] <= 1 ? 'bg-danger' : 
                                                              ($leave['days_remaining'] <= 3 ? 'bg-warning' : 'bg-info');
                                                ?>
                                                <span class="badge <?php echo $badge_class; ?>">
                                                    <?php echo $leave['days_remaining']; ?> يوم
                                                </span>
                                            </td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <?php endif; ?>
        </main>
    </div>
</div>

<script>
// تحديث التنبيهات
function refreshAlerts() {
    showLoading('جاري تحديث التنبيهات...');
    location.reload();
}

// تحديث تلقائي كل 5 دقائق
setInterval(function() {
    // يمكن إضافة تحديث AJAX هنا بدلاً من إعادة تحميل الصفحة
}, 300000); // 5 دقائق
</script>

<?php include '../includes/footer.php'; ?>
