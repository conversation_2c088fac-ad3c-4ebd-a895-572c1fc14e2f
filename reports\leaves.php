<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit();
}

$page_title = 'تقارير الإجازات';

// معالجة فلاتر البحث
$department_id = $_GET['department_id'] ?? '';
$leave_type_id = $_GET['leave_type_id'] ?? '';
$status = $_GET['status'] ?? '';
$year = $_GET['year'] ?? date('Y');
$month = $_GET['month'] ?? '';

try {
    $pdo = getConnection();
    
    // الحصول على قائمة الأقسام
    $departments_stmt = $pdo->query("
        SELECT id, name FROM departments 
        WHERE status = 'active' 
        ORDER BY name
    ");
    $departments = $departments_stmt->fetchAll();
    
    // الحصول على قائمة أنواع الإجازات
    $leave_types_stmt = $pdo->query("
        SELECT id, name FROM leave_types 
        WHERE status = 'active' 
        ORDER BY name
    ");
    $leave_types = $leave_types_stmt->fetchAll();
    
    // بناء استعلام التقرير
    $where_conditions = ["1=1"];
    $params = [];
    
    if ($department_id) {
        $where_conditions[] = "d.id = ?";
        $params[] = $department_id;
    }
    
    if ($leave_type_id) {
        $where_conditions[] = "lr.leave_type_id = ?";
        $params[] = $leave_type_id;
    }
    
    if ($status) {
        $where_conditions[] = "lr.status = ?";
        $params[] = $status;
    }
    
    if ($year) {
        $where_conditions[] = "YEAR(lr.start_date) = ?";
        $params[] = $year;
    }
    
    if ($month) {
        $where_conditions[] = "MONTH(lr.start_date) = ?";
        $params[] = $month;
    }
    
    $where_clause = implode(' AND ', $where_conditions);
    
    // الحصول على بيانات طلبات الإجازات
    $leaves_stmt = $pdo->prepare("
        SELECT 
            lr.*,
            e.first_name, e.last_name, e.employee_number,
            d.name as department_name,
            lt.name as leave_type_name,
            DATEDIFF(lr.end_date, lr.start_date) + 1 as total_days
        FROM leave_requests lr
        JOIN employees e ON lr.employee_id = e.id
        LEFT JOIN departments d ON e.department_id = d.id
        JOIN leave_types lt ON lr.leave_type_id = lt.id
        WHERE $where_clause
        ORDER BY lr.created_at DESC
    ");
    $leaves_stmt->execute($params);
    $leaves = $leaves_stmt->fetchAll();
    
    // حساب الإحصائيات
    $total_requests = count($leaves);
    $approved_requests = count(array_filter($leaves, function($l) { return $l['status'] == 'approved'; }));
    $pending_requests = count(array_filter($leaves, function($l) { return $l['status'] == 'pending'; }));
    $rejected_requests = count(array_filter($leaves, function($l) { return $l['status'] == 'rejected'; }));
    
    $total_days = array_sum(array_column($leaves, 'total_days'));
    $approved_days = array_sum(array_map(function($l) { 
        return $l['status'] == 'approved' ? $l['total_days'] : 0; 
    }, $leaves));
    
    // إحصائيات أنواع الإجازات
    $leave_types_stats = [];
    foreach ($leaves as $leave) {
        $type = $leave['leave_type_name'];
        if (!isset($leave_types_stats[$type])) {
            $leave_types_stats[$type] = ['count' => 0, 'days' => 0];
        }
        $leave_types_stats[$type]['count']++;
        if ($leave['status'] == 'approved') {
            $leave_types_stats[$type]['days'] += $leave['total_days'];
        }
    }
    
    // إحصائيات شهرية
    $monthly_stats = [];
    for ($i = 1; $i <= 12; $i++) {
        $monthly_stats[$i] = ['count' => 0, 'days' => 0];
    }
    
    foreach ($leaves as $leave) {
        $month_num = (int)date('n', strtotime($leave['start_date']));
        $monthly_stats[$month_num]['count']++;
        if ($leave['status'] == 'approved') {
            $monthly_stats[$month_num]['days'] += $leave['total_days'];
        }
    }
    
} catch (PDOException $e) {
    $leaves = [];
    $departments = [];
    $leave_types = [];
}

include '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
            <div class="position-sticky pt-3">
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link" href="../index.php">
                            <i class="fas fa-tachometer-alt me-2"></i>
                            لوحة التحكم
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">
                            <i class="fas fa-chart-pie me-2"></i>
                            تقارير النظام
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="employees.php">
                            <i class="fas fa-users me-2"></i>
                            تقارير الموظفين
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../attendance/reports.php">
                            <i class="fas fa-clock me-2"></i>
                            تقارير الحضور
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="leaves.php">
                            <i class="fas fa-calendar-alt me-2"></i>
                            تقارير الإجازات
                        </a>
                    </li>
                </ul>
            </div>
        </nav>
        
        <!-- Main Content -->
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">
                    <i class="fas fa-calendar-alt me-2"></i>
                    تقارير الإجازات
                </h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <button type="button" class="btn btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
                            <i class="fas fa-download me-1"></i>
                            تصدير التقرير
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" onclick="exportLeaves('excel')">
                                <i class="fas fa-file-excel me-2"></i>Excel
                            </a></li>
                            <li><a class="dropdown-item" href="#" onclick="exportLeaves('pdf')">
                                <i class="fas fa-file-pdf me-2"></i>PDF
                            </a></li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- فلاتر التقرير -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-filter me-2"></i>
                        فلاتر التقرير
                    </h6>
                </div>
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-2">
                            <label for="department_id" class="form-label">القسم</label>
                            <select class="form-select" id="department_id" name="department_id">
                                <option value="">جميع الأقسام</option>
                                <?php foreach ($departments as $dept): ?>
                                    <option value="<?php echo $dept['id']; ?>" 
                                            <?php echo $department_id == $dept['id'] ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($dept['name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="leave_type_id" class="form-label">نوع الإجازة</label>
                            <select class="form-select" id="leave_type_id" name="leave_type_id">
                                <option value="">جميع الأنواع</option>
                                <?php foreach ($leave_types as $type): ?>
                                    <option value="<?php echo $type['id']; ?>" 
                                            <?php echo $leave_type_id == $type['id'] ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($type['name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="status" class="form-label">الحالة</label>
                            <select class="form-select" id="status" name="status">
                                <option value="">جميع الحالات</option>
                                <option value="pending" <?php echo $status == 'pending' ? 'selected' : ''; ?>>قيد المراجعة</option>
                                <option value="approved" <?php echo $status == 'approved' ? 'selected' : ''; ?>>موافق عليها</option>
                                <option value="rejected" <?php echo $status == 'rejected' ? 'selected' : ''; ?>>مرفوضة</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="year" class="form-label">السنة</label>
                            <select class="form-select" id="year" name="year">
                                <?php for ($y = date('Y'); $y >= date('Y') - 5; $y--): ?>
                                    <option value="<?php echo $y; ?>" <?php echo $year == $y ? 'selected' : ''; ?>>
                                        <?php echo $y; ?>
                                    </option>
                                <?php endfor; ?>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="month" class="form-label">الشهر</label>
                            <select class="form-select" id="month" name="month">
                                <option value="">جميع الشهور</option>
                                <?php 
                                $months = [
                                    1 => 'يناير', 2 => 'فبراير', 3 => 'مارس', 4 => 'أبريل',
                                    5 => 'مايو', 6 => 'يونيو', 7 => 'يوليو', 8 => 'أغسطس',
                                    9 => 'سبتمبر', 10 => 'أكتوبر', 11 => 'نوفمبر', 12 => 'ديسمبر'
                                ];
                                foreach ($months as $num => $name): ?>
                                    <option value="<?php echo $num; ?>" <?php echo $month == $num ? 'selected' : ''; ?>>
                                        <?php echo $name; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-2 d-flex align-items-end">
                            <button type="submit" class="btn btn-primary me-2">
                                <i class="fas fa-search me-1"></i>
                                تطبيق
                            </button>
                            <a href="leaves.php" class="btn btn-outline-secondary">
                                <i class="fas fa-undo me-1"></i>
                                إعادة تعيين
                            </a>
                        </div>
                    </form>
                </div>
            </div>

            <!-- إحصائيات الإجازات -->
            <div class="row mb-4">
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card stats-card border-0 shadow-sm">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                        إجمالي الطلبات
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800 counter">
                                        <?php echo $total_requests; ?>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-calendar-alt fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card stats-card success border-0 shadow-sm">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                        الطلبات المعتمدة
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800 counter">
                                        <?php echo $approved_requests; ?>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card stats-card warning border-0 shadow-sm">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                        قيد المراجعة
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800 counter">
                                        <?php echo $pending_requests; ?>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-clock fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card stats-card info border-0 shadow-sm">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                        إجمالي الأيام المعتمدة
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800 counter">
                                        <?php echo $approved_days; ?>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-calendar-day fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mb-4">
                <!-- توزيع أنواع الإجازات -->
                <div class="col-lg-6">
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">
                                <i class="fas fa-chart-pie me-2"></i>
                                توزيع أنواع الإجازات
                            </h6>
                        </div>
                        <div class="card-body">
                            <canvas id="leaveTypesChart" width="100%" height="50"></canvas>
                        </div>
                    </div>
                </div>

                <!-- الإحصائيات الشهرية -->
                <div class="col-lg-6">
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">
                                <i class="fas fa-chart-line me-2"></i>
                                الإحصائيات الشهرية لعام <?php echo $year; ?>
                            </h6>
                        </div>
                        <div class="card-body">
                            <canvas id="monthlyChart" width="100%" height="50"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- جدول طلبات الإجازات -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-table me-2"></i>
                        قائمة طلبات الإجازات التفصيلية
                        <small class="text-muted">(<?php echo count($leaves); ?> طلب)</small>
                    </h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered data-table" id="leavesTable" width="100%" cellspacing="0">
                            <thead>
                                <tr>
                                    <th>الموظف</th>
                                    <th>نوع الإجازة</th>
                                    <th>تاريخ البداية</th>
                                    <th>تاريخ النهاية</th>
                                    <th>عدد الأيام</th>
                                    <th>السبب</th>
                                    <th>الحالة</th>
                                    <th>تاريخ الطلب</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($leaves as $leave): ?>
                                <tr>
                                    <td>
                                        <div>
                                            <strong><?php echo htmlspecialchars($leave['first_name'] . ' ' . $leave['last_name']); ?></strong>
                                            <br><small class="text-muted">
                                                رقم: <?php echo htmlspecialchars($leave['employee_number']); ?>
                                                <?php if ($leave['department_name']): ?>
                                                    | <?php echo htmlspecialchars($leave['department_name']); ?>
                                                <?php endif; ?>
                                            </small>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-info">
                                            <?php echo htmlspecialchars($leave['leave_type_name']); ?>
                                        </span>
                                    </td>
                                    <td><?php echo formatDate($leave['start_date']); ?></td>
                                    <td><?php echo formatDate($leave['end_date']); ?></td>
                                    <td>
                                        <span class="badge bg-secondary">
                                            <?php echo $leave['total_days']; ?> يوم
                                        </span>
                                    </td>
                                    <td>
                                        <?php if ($leave['reason']): ?>
                                            <span title="<?php echo htmlspecialchars($leave['reason']); ?>">
                                                <?php echo htmlspecialchars(substr($leave['reason'], 0, 30)) . (strlen($leave['reason']) > 30 ? '...' : ''); ?>
                                            </span>
                                        <?php else: ?>
                                            <span class="text-muted">لا يوجد</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php
                                        $status_classes = [
                                            'pending' => 'warning',
                                            'approved' => 'success',
                                            'rejected' => 'danger'
                                        ];
                                        $status_text = [
                                            'pending' => 'قيد المراجعة',
                                            'approved' => 'معتمدة',
                                            'rejected' => 'مرفوضة'
                                        ];
                                        $status_class = $status_classes[$leave['status']] ?? 'secondary';
                                        ?>
                                        <span class="badge bg-<?php echo $status_class; ?>">
                                            <?php echo $status_text[$leave['status']] ?? $leave['status']; ?>
                                        </span>
                                    </td>
                                    <td><?php echo formatDate($leave['created_at']); ?></td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<script>
// بيانات أنواع الإجازات
const leaveTypesData = {
    labels: [
        <?php foreach ($leave_types_stats as $type => $stats): ?>
            '<?php echo htmlspecialchars($type); ?>',
        <?php endforeach; ?>
    ],
    datasets: [{
        data: [
            <?php foreach ($leave_types_stats as $type => $stats): ?>
                <?php echo $stats['count']; ?>,
            <?php endforeach; ?>
        ],
        backgroundColor: [
            '#4e73df', '#1cc88a', '#36b9cc', '#f6c23e', '#e74a3b',
            '#858796', '#5a5c69', '#6f42c1', '#e83e8c'
        ]
    }]
};

// بيانات الإحصائيات الشهرية
const monthlyData = {
    labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 
             'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'],
    datasets: [{
        label: 'عدد الطلبات',
        data: [
            <?php for ($i = 1; $i <= 12; $i++): ?>
                <?php echo $monthly_stats[$i]['count']; ?>,
            <?php endfor; ?>
        ],
        backgroundColor: '#4e73df',
        borderColor: '#4e73df',
        borderWidth: 2,
        fill: false
    }, {
        label: 'الأيام المعتمدة',
        data: [
            <?php for ($i = 1; $i <= 12; $i++): ?>
                <?php echo $monthly_stats[$i]['days']; ?>,
            <?php endfor; ?>
        ],
        backgroundColor: '#1cc88a',
        borderColor: '#1cc88a',
        borderWidth: 2,
        fill: false
    }]
};

// رسم الرسوم البيانية
$(document).ready(function() {
    // رسم بياني لأنواع الإجازات
    const typesCtx = document.getElementById('leaveTypesChart').getContext('2d');
    new Chart(typesCtx, {
        type: 'pie',
        data: leaveTypesData,
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'bottom',
                }
            }
        }
    });

    // رسم بياني للإحصائيات الشهرية
    const monthlyCtx = document.getElementById('monthlyChart').getContext('2d');
    new Chart(monthlyCtx, {
        type: 'line',
        data: monthlyData,
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'top',
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        stepSize: 1
                    }
                }
            }
        }
    });

    // تهيئة DataTable
    $('#leavesTable').DataTable({
        language: {
            url: 'https://cdn.datatables.net/plug-ins/1.13.6/i18n/ar.json'
        },
        responsive: true,
        pageLength: 25,
        order: [[7, 'desc']],
        dom: 'Bfrtip',
        buttons: [
            'copy', 'csv', 'excel', 'pdf', 'print'
        ]
    });
});

// تصدير تقرير الإجازات
function exportLeaves(format) {
    const params = new URLSearchParams(window.location.search);
    params.set('export', format);
    
    showLoading('جاري تصدير التقرير...');
    
    fetch('export_leaves.php?' + params.toString())
        .then(response => response.blob())
        .then(blob => {
            hideLoading();
            
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `leaves_report_${new Date().toISOString().split('T')[0]}.${format}`;
            a.click();
            window.URL.revokeObjectURL(url);
            
            showSuccess('تم تصدير التقرير بنجاح');
        })
        .catch(error => {
            hideLoading();
            showError('حدث خطأ أثناء تصدير التقرير');
        });
}
</script>

<?php include '../includes/footer.php'; ?>
