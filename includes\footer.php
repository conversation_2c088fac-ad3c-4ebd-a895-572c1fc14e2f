    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    
    <!-- DataTables JS -->
    <script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>
    
    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11.7.27/dist/sweetalert2.all.min.js"></script>
    
    <!-- Custom JavaScript -->
    <script src="assets/js/main.js"></script>
    
    <script>
        // تهيئة DataTables
        $(document).ready(function() {
            $('.data-table').DataTable({
                language: {
                    url: 'https://cdn.datatables.net/plug-ins/1.13.6/i18n/ar.json'
                },
                responsive: true,
                pageLength: 25,
                order: [[0, 'desc']]
            });
            
            // تأثيرات الحركة
            $('.fade-in').each(function(i) {
                $(this).delay(i * 100).queue(function() {
                    $(this).addClass('show').dequeue();
                });
            });
            
            // تحديث الإشعارات
            updateNotifications();
            setInterval(updateNotifications, 30000); // كل 30 ثانية
        });
        
        // دالة تحديث الإشعارات
        function updateNotifications() {
            $.ajax({
                url: 'ajax/get_notifications.php',
                method: 'GET',
                dataType: 'json',
                success: function(data) {
                    $('#notificationCount').text(data.count);
                    if (data.count > 0) {
                        $('#notificationCount').show();
                    } else {
                        $('#notificationCount').hide();
                    }
                }
            });
        }
        
        // دالة عرض رسائل التأكيد
        function confirmDelete(message, callback) {
            Swal.fire({
                title: 'هل أنت متأكد؟',
                text: message || 'لن تتمكن من التراجع عن هذا الإجراء!',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#d33',
                cancelButtonColor: '#3085d6',
                confirmButtonText: 'نعم، احذف!',
                cancelButtonText: 'إلغاء'
            }).then((result) => {
                if (result.isConfirmed && callback) {
                    callback();
                }
            });
        }
        
        // دالة عرض رسائل النجاح
        function showSuccess(message) {
            Swal.fire({
                icon: 'success',
                title: 'تم بنجاح!',
                text: message,
                timer: 3000,
                showConfirmButton: false
            });
        }
        
        // دالة عرض رسائل الخطأ
        function showError(message) {
            Swal.fire({
                icon: 'error',
                title: 'خطأ!',
                text: message
            });
        }
        
        // دالة عرض Loading
        function showLoading() {
            Swal.fire({
                title: 'جاري التحميل...',
                allowOutsideClick: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });
        }
        
        // تأثيرات hover للبطاقات
        $('.card').hover(
            function() {
                $(this).addClass('shadow-lg');
            },
            function() {
                $(this).removeClass('shadow-lg');
            }
        );
        
        // تأثيرات الأزرار
        $('.btn').on('click', function() {
            $(this).addClass('pulse');
            setTimeout(() => {
                $(this).removeClass('pulse');
            }, 600);
        });
        
        // تحديث الوقت الحالي
        function updateCurrentTime() {
            const now = new Date();
            const timeString = now.toLocaleString('ar-SA', {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
            $('#currentTime').text(timeString);
        }
        
        // تحديث الوقت كل ثانية
        setInterval(updateCurrentTime, 1000);
        updateCurrentTime();
        
        // تأثير الكتابة للعناوين
        function typeWriter(element, text, speed = 50) {
            let i = 0;
            element.innerHTML = '';
            function type() {
                if (i < text.length) {
                    element.innerHTML += text.charAt(i);
                    i++;
                    setTimeout(type, speed);
                }
            }
            type();
        }
        
        // تطبيق تأثير الكتابة على العناوين الرئيسية
        $('.typewriter').each(function() {
            const text = $(this).text();
            typeWriter(this, text);
        });
        
        // تأثير العد التصاعدي للأرقام
        function animateCounter(element, target, duration = 2000) {
            let start = 0;
            const increment = target / (duration / 16);
            
            function updateCounter() {
                start += increment;
                if (start < target) {
                    element.textContent = Math.floor(start);
                    requestAnimationFrame(updateCounter);
                } else {
                    element.textContent = target;
                }
            }
            updateCounter();
        }
        
        // تطبيق تأثير العد على الأرقام في البطاقات
        $('.counter').each(function() {
            const target = parseInt($(this).text());
            animateCounter(this, target);
        });
        
        // تأثير التمرير السلس
        $('a[href^="#"]').on('click', function(e) {
            e.preventDefault();
            const target = $(this.getAttribute('href'));
            if (target.length) {
                $('html, body').animate({
                    scrollTop: target.offset().top - 100
                }, 800);
            }
        });
        
        // إضافة تأثيرات عند التمرير
        $(window).scroll(function() {
            const scrollTop = $(this).scrollTop();
            
            // تأثير parallax للخلفية
            $('.parallax').css('transform', `translateY(${scrollTop * 0.5}px)`);
            
            // إظهار/إخفاء زر العودة للأعلى
            if (scrollTop > 300) {
                $('#backToTop').fadeIn();
            } else {
                $('#backToTop').fadeOut();
            }
        });
        
        // زر العودة للأعلى
        $('#backToTop').on('click', function() {
            $('html, body').animate({scrollTop: 0}, 800);
        });
    </script>
    
    <!-- زر العودة للأعلى -->
    <button id="backToTop" class="btn btn-primary position-fixed" style="bottom: 20px; left: 20px; display: none; z-index: 1000; border-radius: 50%; width: 50px; height: 50px;">
        <i class="fas fa-arrow-up"></i>
    </button>
    
    <!-- شريط الوقت الحالي -->
    <div class="position-fixed bottom-0 end-0 p-3" style="z-index: 1000;">
        <div class="bg-dark text-white px-3 py-2 rounded">
            <small id="currentTime"></small>
        </div>
    </div>
</body>
</html>
