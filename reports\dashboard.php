<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit();
}

$page_title = 'تقارير النظام';

try {
    $pdo = getConnection();
    
    // إحصائيات عامة
    $stats = [];
    
    // إحصائيات الموظفين
    $emp_stats = $pdo->query("
        SELECT 
            COUNT(*) as total_employees,
            COUNT(CASE WHEN status = 'active' THEN 1 END) as active_employees,
            COUNT(CASE WHEN status = 'terminated' THEN 1 END) as terminated_employees,
            COUNT(CASE WHEN gender = 'male' THEN 1 END) as male_employees,
            COUNT(CASE WHEN gender = 'female' THEN 1 END) as female_employees
        FROM employees
    ")->fetch();
    
    // إحصائيات الحضور لهذا الشهر
    $attendance_stats = $pdo->query("
        SELECT 
            COUNT(*) as total_attendance,
            COUNT(CASE WHEN status = 'حاضر' THEN 1 END) as present_days,
            COUNT(CASE WHEN status = 'غائب' THEN 1 END) as absent_days,
            COUNT(CASE WHEN late_minutes > 0 THEN 1 END) as late_days,
            SUM(CASE WHEN overtime_hours > 0 THEN overtime_hours ELSE 0 END) as total_overtime
        FROM attendance 
        WHERE MONTH(date) = MONTH(CURDATE()) AND YEAR(date) = YEAR(CURDATE())
    ")->fetch();
    
    // إحصائيات الإجازات لهذا العام
    $leave_stats = $pdo->query("
        SELECT 
            COUNT(*) as total_requests,
            COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_requests,
            COUNT(CASE WHEN status = 'approved' THEN 1 END) as approved_requests,
            COUNT(CASE WHEN status = 'rejected' THEN 1 END) as rejected_requests,
            SUM(CASE WHEN status = 'approved' THEN days_requested ELSE 0 END) as total_leave_days
        FROM leave_requests 
        WHERE YEAR(created_at) = YEAR(CURDATE())
    ")->fetch();
    
    // إحصائيات الأقسام
    $dept_stats = $pdo->query("
        SELECT 
            d.name as department_name,
            COUNT(e.id) as employee_count
        FROM departments d
        LEFT JOIN employees e ON d.id = e.department_id AND e.status = 'active'
        WHERE d.status = 'active'
        GROUP BY d.id, d.name
        ORDER BY employee_count DESC
    ")->fetchAll();
    
    // الحضور اليومي لآخر 30 يوم
    $daily_attendance = $pdo->query("
        SELECT 
            DATE(a.date) as attendance_date,
            COUNT(CASE WHEN a.status = 'حاضر' THEN 1 END) as present_count,
            COUNT(CASE WHEN a.status = 'غائب' THEN 1 END) as absent_count,
            COUNT(CASE WHEN a.late_minutes > 0 THEN 1 END) as late_count
        FROM attendance a
        WHERE a.date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
        GROUP BY DATE(a.date)
        ORDER BY attendance_date ASC
    ")->fetchAll();
    
    // أنواع الإجازات الأكثر استخداماً
    $leave_types_usage = $pdo->query("
        SELECT 
            lt.name as leave_type_name,
            lt.color as leave_type_color,
            COUNT(lr.id) as request_count,
            SUM(lr.days_requested) as total_days
        FROM leave_types lt
        LEFT JOIN leave_requests lr ON lt.id = lr.leave_type_id 
            AND lr.status = 'approved' 
            AND YEAR(lr.created_at) = YEAR(CURDATE())
        WHERE lt.status = 'active'
        GROUP BY lt.id, lt.name, lt.color
        ORDER BY request_count DESC
    ")->fetchAll();
    
} catch (PDOException $e) {
    $emp_stats = [];
    $attendance_stats = [];
    $leave_stats = [];
    $dept_stats = [];
    $daily_attendance = [];
    $leave_types_usage = [];
}

include '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
            <div class="position-sticky pt-3">
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link" href="../index.php">
                            <i class="fas fa-tachometer-alt me-2"></i>
                            لوحة التحكم
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="dashboard.php">
                            <i class="fas fa-chart-pie me-2"></i>
                            تقارير النظام
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="employees.php">
                            <i class="fas fa-users me-2"></i>
                            تقارير الموظفين
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../attendance/reports.php">
                            <i class="fas fa-clock me-2"></i>
                            تقارير الحضور
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="leaves.php">
                            <i class="fas fa-calendar-alt me-2"></i>
                            تقارير الإجازات
                        </a>
                    </li>
                </ul>
            </div>
        </nav>
        
        <!-- Main Content -->
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">
                    <i class="fas fa-chart-pie me-2"></i>
                    تقارير النظام
                </h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <button type="button" class="btn btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
                            <i class="fas fa-download me-1"></i>
                            تصدير التقرير
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" onclick="exportDashboard('pdf')">
                                <i class="fas fa-file-pdf me-2"></i>PDF
                            </a></li>
                            <li><a class="dropdown-item" href="#" onclick="printDashboard()">
                                <i class="fas fa-print me-2"></i>طباعة
                            </a></li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- إحصائيات عامة -->
            <div class="row mb-4">
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card stats-card border-0 shadow-sm border-left-primary">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                        إجمالي الموظفين
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800 counter">
                                        <?php echo $emp_stats['total_employees'] ?? 0; ?>
                                    </div>
                                    <small class="text-success">
                                        نشط: <?php echo $emp_stats['active_employees'] ?? 0; ?>
                                    </small>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-users fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card stats-card border-0 shadow-sm border-left-success">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                        الحضور هذا الشهر
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800 counter">
                                        <?php echo $attendance_stats['present_days'] ?? 0; ?>
                                    </div>
                                    <small class="text-warning">
                                        تأخير: <?php echo $attendance_stats['late_days'] ?? 0; ?>
                                    </small>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-user-check fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card stats-card border-0 shadow-sm border-left-info">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                        طلبات الإجازات
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800 counter">
                                        <?php echo $leave_stats['total_requests'] ?? 0; ?>
                                    </div>
                                    <small class="text-warning">
                                        معلق: <?php echo $leave_stats['pending_requests'] ?? 0; ?>
                                    </small>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-calendar-alt fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card stats-card border-0 shadow-sm border-left-warning">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                        الساعات الإضافية
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800 counter">
                                        <?php echo number_format($attendance_stats['total_overtime'] ?? 0, 1); ?>
                                    </div>
                                    <small class="text-muted">ساعة هذا الشهر</small>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-clock fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <!-- رسم بياني للحضور اليومي -->
                <div class="col-xl-8 col-lg-7">
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">
                                <i class="fas fa-chart-line me-2"></i>
                                الحضور اليومي - آخر 30 يوم
                            </h6>
                        </div>
                        <div class="card-body">
                            <canvas id="attendanceChart" width="100%" height="40"></canvas>
                        </div>
                    </div>
                </div>

                <!-- توزيع الموظفين حسب الأقسام -->
                <div class="col-xl-4 col-lg-5">
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">
                                <i class="fas fa-chart-pie me-2"></i>
                                توزيع الموظفين حسب الأقسام
                            </h6>
                        </div>
                        <div class="card-body">
                            <canvas id="departmentChart" width="100%" height="100"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <!-- أنواع الإجازات الأكثر استخداماً -->
                <div class="col-xl-6 col-lg-6">
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">
                                <i class="fas fa-chart-bar me-2"></i>
                                أنواع الإجازات الأكثر استخداماً
                            </h6>
                        </div>
                        <div class="card-body">
                            <canvas id="leaveTypesChart" width="100%" height="50"></canvas>
                        </div>
                    </div>
                </div>

                <!-- إحصائيات تفصيلية -->
                <div class="col-xl-6 col-lg-6">
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">
                                <i class="fas fa-list me-2"></i>
                                إحصائيات تفصيلية
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-6 mb-3">
                                    <div class="card bg-primary text-white">
                                        <div class="card-body text-center">
                                            <h4><?php echo $emp_stats['male_employees'] ?? 0; ?></h4>
                                            <small>موظفين ذكور</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-6 mb-3">
                                    <div class="card bg-info text-white">
                                        <div class="card-body text-center">
                                            <h4><?php echo $emp_stats['female_employees'] ?? 0; ?></h4>
                                            <small>موظفات إناث</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-6 mb-3">
                                    <div class="card bg-success text-white">
                                        <div class="card-body text-center">
                                            <h4><?php echo $leave_stats['approved_requests'] ?? 0; ?></h4>
                                            <small>إجازات مقبولة</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-6 mb-3">
                                    <div class="card bg-danger text-white">
                                        <div class="card-body text-center">
                                            <h4><?php echo $leave_stats['rejected_requests'] ?? 0; ?></h4>
                                            <small>إجازات مرفوضة</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- جدول الأقسام -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-building me-2"></i>
                        توزيع الموظفين حسب الأقسام
                    </h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered" width="100%" cellspacing="0">
                            <thead>
                                <tr>
                                    <th>القسم</th>
                                    <th>عدد الموظفين</th>
                                    <th>النسبة</th>
                                    <th>التمثيل البصري</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php 
                                $total_employees = array_sum(array_column($dept_stats, 'employee_count'));
                                foreach ($dept_stats as $dept): 
                                    $percentage = $total_employees > 0 ? ($dept['employee_count'] / $total_employees) * 100 : 0;
                                ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($dept['department_name']); ?></td>
                                    <td>
                                        <span class="badge bg-primary">
                                            <?php echo $dept['employee_count']; ?>
                                        </span>
                                    </td>
                                    <td><?php echo number_format($percentage, 1); ?>%</td>
                                    <td>
                                        <div class="progress" style="height: 20px;">
                                            <div class="progress-bar bg-primary" style="width: <?php echo $percentage; ?>%">
                                                <?php echo number_format($percentage, 1); ?>%
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<script>
// بيانات الحضور اليومي
const attendanceData = {
    labels: [
        <?php foreach ($daily_attendance as $day): ?>
            '<?php echo formatDate($day['attendance_date']); ?>',
        <?php endforeach; ?>
    ],
    datasets: [{
        label: 'حاضر',
        data: [
            <?php foreach ($daily_attendance as $day): ?>
                <?php echo $day['present_count']; ?>,
            <?php endforeach; ?>
        ],
        borderColor: '#28a745',
        backgroundColor: 'rgba(40, 167, 69, 0.1)',
        borderWidth: 2,
        fill: true
    }, {
        label: 'غائب',
        data: [
            <?php foreach ($daily_attendance as $day): ?>
                <?php echo $day['absent_count']; ?>,
            <?php endforeach; ?>
        ],
        borderColor: '#dc3545',
        backgroundColor: 'rgba(220, 53, 69, 0.1)',
        borderWidth: 2,
        fill: true
    }, {
        label: 'متأخر',
        data: [
            <?php foreach ($daily_attendance as $day): ?>
                <?php echo $day['late_count']; ?>,
            <?php endforeach; ?>
        ],
        borderColor: '#ffc107',
        backgroundColor: 'rgba(255, 193, 7, 0.1)',
        borderWidth: 2,
        fill: true
    }]
};

// بيانات الأقسام
const departmentData = {
    labels: [
        <?php foreach ($dept_stats as $dept): ?>
            '<?php echo htmlspecialchars($dept['department_name']); ?>',
        <?php endforeach; ?>
    ],
    datasets: [{
        data: [
            <?php foreach ($dept_stats as $dept): ?>
                <?php echo $dept['employee_count']; ?>,
            <?php endforeach; ?>
        ],
        backgroundColor: [
            '#4e73df', '#1cc88a', '#36b9cc', '#f6c23e', '#e74a3b',
            '#858796', '#5a5c69', '#6f42c1', '#e83e8c', '#fd7e14'
        ]
    }]
};

// بيانات أنواع الإجازات
const leaveTypesData = {
    labels: [
        <?php foreach ($leave_types_usage as $type): ?>
            '<?php echo htmlspecialchars($type['leave_type_name']); ?>',
        <?php endforeach; ?>
    ],
    datasets: [{
        label: 'عدد الطلبات',
        data: [
            <?php foreach ($leave_types_usage as $type): ?>
                <?php echo $type['request_count']; ?>,
            <?php endforeach; ?>
        ],
        backgroundColor: [
            <?php foreach ($leave_types_usage as $type): ?>
                '<?php echo $type['leave_type_color'] ?: '#6c757d'; ?>',
            <?php endforeach; ?>
        ]
    }]
};

// رسم الرسوم البيانية
$(document).ready(function() {
    // رسم بياني للحضور
    const attendanceCtx = document.getElementById('attendanceChart').getContext('2d');
    new Chart(attendanceCtx, {
        type: 'line',
        data: attendanceData,
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'top',
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        stepSize: 1
                    }
                }
            }
        }
    });

    // رسم بياني للأقسام
    const departmentCtx = document.getElementById('departmentChart').getContext('2d');
    new Chart(departmentCtx, {
        type: 'doughnut',
        data: departmentData,
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'bottom',
                }
            }
        }
    });

    // رسم بياني لأنواع الإجازات
    const leaveTypesCtx = document.getElementById('leaveTypesChart').getContext('2d');
    new Chart(leaveTypesCtx, {
        type: 'bar',
        data: leaveTypesData,
        options: {
            responsive: true,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        stepSize: 1
                    }
                }
            }
        }
    });
});

// تصدير التقرير
function exportDashboard(format) {
    showLoading('جاري تصدير التقرير...');
    
    fetch(`export_dashboard.php?format=${format}`)
        .then(response => response.blob())
        .then(blob => {
            hideLoading();
            
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `dashboard_report_${new Date().toISOString().split('T')[0]}.${format}`;
            a.click();
            window.URL.revokeObjectURL(url);
            
            showSuccess('تم تصدير التقرير بنجاح');
        })
        .catch(error => {
            hideLoading();
            showError('حدث خطأ أثناء تصدير التقرير');
        });
}

// طباعة التقرير
function printDashboard() {
    window.print();
}
</script>

<?php include '../includes/footer.php'; ?>
