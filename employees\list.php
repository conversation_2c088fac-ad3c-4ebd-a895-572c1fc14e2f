<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit();
}

$page_title = 'قائمة الموظفين';

// الحصول على قائمة الموظفين
try {
    $pdo = getConnection();
    $stmt = $pdo->query("
        SELECT 
            e.*,
            d.name as department_name,
            p.title as position_title
        FROM employees e
        LEFT JOIN departments d ON e.department_id = d.id
        LEFT JOIN positions p ON e.position_id = p.id
        ORDER BY e.created_at DESC
    ");
    $employees = $stmt->fetchAll();
} catch (PDOException $e) {
    $employees = [];
    $error_message = 'خطأ في تحميل بيانات الموظفين';
}

include '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
            <div class="position-sticky pt-3">
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link" href="../index.php">
                            <i class="fas fa-tachometer-alt me-2"></i>
                            لوحة التحكم
                        </a>
                    </li>

                    <li class="nav-item">
                        <a class="nav-link active" href="list.php">
                            <i class="fas fa-users me-2"></i>
                            قائمة الموظفين
                        </a>
                    </li>

                    <li class="nav-item">
                        <a class="nav-link" href="add.php">
                            <i class="fas fa-plus me-2"></i>
                            إضافة موظف
                        </a>
                    </li>

                    <li class="nav-item">
                        <a class="nav-link" href="../attendance/record.php">
                            <i class="fas fa-clock me-2"></i>
                            الحضور والانصراف
                        </a>
                    </li>

                    <li class="nav-item">
                        <a class="nav-link" href="../leaves/request.php">
                            <i class="fas fa-calendar-times me-2"></i>
                            إدارة الإجازات
                        </a>
                    </li>
                </ul>
            </div>
        </nav>
        
        <!-- Main Content -->
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">
                    <i class="fas fa-users me-2"></i>
                    قائمة الموظفين
                </h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <a href="add.php" class="btn btn-primary">
                            <i class="fas fa-plus me-1"></i>
                            إضافة موظف جديد
                        </a>
                        <button type="button" class="btn btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
                            <i class="fas fa-download me-1"></i>
                            تصدير
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" onclick="exportData('excel', 'export.php', 'employees.xlsx')">
                                <i class="fas fa-file-excel me-2"></i>Excel
                            </a></li>
                            <li><a class="dropdown-item" href="#" onclick="exportData('pdf', 'export.php', 'employees.pdf')">
                                <i class="fas fa-file-pdf me-2"></i>PDF
                            </a></li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- إحصائيات سريعة -->
            <div class="row mb-4">
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card stats-card border-0 shadow-sm">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                        إجمالي الموظفين
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800 counter">
                                        <?php echo count($employees); ?>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-users fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card stats-card success border-0 shadow-sm">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                        الموظفون النشطون
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800 counter">
                                        <?php echo count(array_filter($employees, function($emp) { return $emp['status'] == 'active'; })); ?>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-user-check fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card stats-card info border-0 shadow-sm">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                        الموظفون الجدد هذا الشهر
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800 counter">
                                        <?php 
                                        $this_month = date('Y-m');
                                        echo count(array_filter($employees, function($emp) use ($this_month) { 
                                            return strpos($emp['created_at'], $this_month) === 0; 
                                        })); 
                                        ?>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-user-plus fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card stats-card warning border-0 shadow-sm">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                        العقود المنتهية قريباً
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800 counter">
                                        <?php echo getExpiringContracts(); ?>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- فلاتر البحث -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-search me-2"></i>
                        البحث والفلترة
                    </h6>
                </div>
                <div class="card-body">
                    <form id="searchForm" class="row g-3">
                        <div class="col-md-3">
                            <label for="search_name" class="form-label">الاسم</label>
                            <input type="text" class="form-control" id="search_name" placeholder="البحث بالاسم">
                        </div>
                        <div class="col-md-3">
                            <label for="search_department" class="form-label">القسم</label>
                            <select class="form-select" id="search_department">
                                <option value="">جميع الأقسام</option>
                                <?php
                                $departments = array_unique(array_column($employees, 'department_name'));
                                foreach ($departments as $dept) {
                                    if ($dept) echo "<option value='$dept'>$dept</option>";
                                }
                                ?>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="search_status" class="form-label">الحالة</label>
                            <select class="form-select" id="search_status">
                                <option value="">جميع الحالات</option>
                                <option value="active">نشط</option>
                                <option value="inactive">غير نشط</option>
                                <option value="terminated">منتهي الخدمة</option>
                                <option value="resigned">مستقيل</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid">
                                <button type="button" class="btn btn-primary" onclick="filterEmployees()">
                                    <i class="fas fa-search me-1"></i>
                                    بحث
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- جدول الموظفين -->
            <div class="card shadow mb-4 fade-in">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-table me-2"></i>
                        بيانات الموظفين
                    </h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered data-table" id="employeesTable" width="100%" cellspacing="0">
                            <thead>
                                <tr>
                                    <th>الرقم الوظيفي</th>
                                    <th>الاسم الكامل</th>
                                    <th>القسم</th>
                                    <th>المنصب</th>
                                    <th>تاريخ التوظيف</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($employees as $employee): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($employee['employee_number']); ?></td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-sm me-3">
                                                <div class="avatar-title bg-primary rounded-circle">
                                                    <?php echo strtoupper(substr($employee['first_name'], 0, 1) . substr($employee['last_name'], 0, 1)); ?>
                                                </div>
                                            </div>
                                            <div>
                                                <strong><?php echo htmlspecialchars($employee['first_name'] . ' ' . $employee['last_name']); ?></strong>
                                                <br>
                                                <small class="text-muted"><?php echo htmlspecialchars($employee['email']); ?></small>
                                            </div>
                                        </div>
                                    </td>
                                    <td><?php echo htmlspecialchars($employee['department_name'] ?: 'غير محدد'); ?></td>
                                    <td><?php echo htmlspecialchars($employee['position_title'] ?: 'غير محدد'); ?></td>
                                    <td><?php echo formatDate($employee['hire_date']); ?></td>
                                    <td>
                                        <?php
                                        $status_classes = [
                                            'active' => 'success',
                                            'inactive' => 'warning',
                                            'terminated' => 'danger',
                                            'resigned' => 'secondary'
                                        ];
                                        $status_labels = [
                                            'active' => 'نشط',
                                            'inactive' => 'غير نشط',
                                            'terminated' => 'منتهي الخدمة',
                                            'resigned' => 'مستقيل'
                                        ];
                                        $status_class = $status_classes[$employee['status']] ?? 'secondary';
                                        $status_label = $status_labels[$employee['status']] ?? $employee['status'];
                                        ?>
                                        <span class="badge bg-<?php echo $status_class; ?>">
                                            <?php echo $status_label; ?>
                                        </span>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="view.php?id=<?php echo $employee['id']; ?>" 
                                               class="btn btn-sm btn-outline-info" 
                                               data-bs-toggle="tooltip" 
                                               title="عرض التفاصيل">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="edit.php?id=<?php echo $employee['id']; ?>" 
                                               class="btn btn-sm btn-outline-primary" 
                                               data-bs-toggle="tooltip" 
                                               title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button type="button" 
                                                    class="btn btn-sm btn-outline-danger" 
                                                    data-bs-toggle="tooltip" 
                                                    title="حذف"
                                                    onclick="deleteEmployee(<?php echo $employee['id']; ?>)">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<style>
.avatar-sm {
    width: 40px;
    height: 40px;
}

.avatar-title {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    font-weight: 600;
}

.stats-card::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 4px;
    height: 100%;
    background: var(--bs-primary);
}

.stats-card.success::before {
    background: var(--bs-success);
}

.stats-card.info::before {
    background: var(--bs-info);
}

.stats-card.warning::before {
    background: var(--bs-warning);
}
</style>

<script>
function filterEmployees() {
    const table = $('#employeesTable').DataTable();
    
    const name = $('#search_name').val();
    const department = $('#search_department').val();
    const status = $('#search_status').val();
    
    // تطبيق الفلاتر
    table.columns(1).search(name);
    table.columns(2).search(department);
    table.columns(5).search(status);
    
    table.draw();
}

function deleteEmployee(id) {
    confirmDelete('هل أنت متأكد من حذف هذا الموظف؟', function() {
        $.ajax({
            url: 'delete.php',
            method: 'POST',
            data: { id: id },
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    showSuccess('تم حذف الموظف بنجاح');
                    setTimeout(() => {
                        location.reload();
                    }, 1500);
                } else {
                    showError(response.message || 'حدث خطأ أثناء الحذف');
                }
            },
            error: function() {
                showError('حدث خطأ في الاتصال بالخادم');
            }
        });
    });
}

// تطبيق البحث السريع
$('#search_name').on('keyup', function() {
    $('#employeesTable').DataTable().columns(1).search(this.value).draw();
});
</script>

<?php include '../includes/footer.php'; ?>
