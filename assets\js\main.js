// ملف JavaScript الرئيسي لنظام إدارة الموظفين

$(document).ready(function() {
    // تهيئة التطبيق
    initializeApp();
    
    // تهيئة DataTables
    initializeDataTables();
    
    // تهيئة الرسوم البيانية
    initializeCharts();
    
    // تهيئة التأثيرات البصرية
    initializeAnimations();
    
    // تهيئة الإشعارات
    initializeNotifications();
});

// دالة تهيئة التطبيق
function initializeApp() {
    // تحديث الوقت الحالي
    updateCurrentTime();
    setInterval(updateCurrentTime, 1000);
    
    // تهيئة tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // تهيئة popovers
    var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });
    
    // تهيئة الشريط الجانبي للهواتف المحمولة
    initializeMobileSidebar();
    
    // تهيئة زر العودة للأعلى
    initializeScrollToTop();
}

// دالة تهيئة DataTables
function initializeDataTables() {
    if ($.fn.DataTable) {
        $('.data-table').DataTable({
            language: {
                url: 'https://cdn.datatables.net/plug-ins/1.13.6/i18n/ar.json'
            },
            responsive: true,
            pageLength: 25,
            order: [[0, 'desc']],
            dom: 'Bfrtip',
            buttons: [
                {
                    extend: 'excel',
                    text: '<i class="fas fa-file-excel me-1"></i> Excel',
                    className: 'btn btn-success btn-sm'
                },
                {
                    extend: 'pdf',
                    text: '<i class="fas fa-file-pdf me-1"></i> PDF',
                    className: 'btn btn-danger btn-sm'
                },
                {
                    extend: 'print',
                    text: '<i class="fas fa-print me-1"></i> طباعة',
                    className: 'btn btn-info btn-sm'
                }
            ],
            initComplete: function() {
                // إضافة تأثيرات للجدول
                $('.dataTables_wrapper').addClass('fade-in');
            }
        });
    }
}

// دالة تهيئة الرسوم البيانية
function initializeCharts() {
    // رسم بياني للحضور الشهري
    const attendanceCtx = document.getElementById('attendanceChart');
    if (attendanceCtx) {
        new Chart(attendanceCtx, {
            type: 'line',
            data: {
                labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
                datasets: [{
                    label: 'معدل الحضور',
                    data: [85, 90, 88, 92, 87, 94],
                    borderColor: '#4e73df',
                    backgroundColor: 'rgba(78, 115, 223, 0.1)',
                    borderWidth: 3,
                    fill: true,
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 100,
                        ticks: {
                            callback: function(value) {
                                return value + '%';
                            }
                        }
                    }
                },
                animation: {
                    duration: 2000,
                    easing: 'easeInOutQuart'
                }
            }
        });
    }
    
    // رسم بياني دائري للأقسام
    const departmentCtx = document.getElementById('departmentChart');
    if (departmentCtx) {
        new Chart(departmentCtx, {
            type: 'doughnut',
            data: {
                labels: ['الموارد البشرية', 'المالية', 'تقنية المعلومات', 'التسويق', 'العمليات'],
                datasets: [{
                    data: [15, 25, 20, 18, 22],
                    backgroundColor: [
                        '#4e73df',
                        '#1cc88a',
                        '#36b9cc',
                        '#f6c23e',
                        '#e74a3b'
                    ],
                    borderWidth: 0
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                },
                animation: {
                    animateRotate: true,
                    duration: 2000
                }
            }
        });
    }
}

// دالة تهيئة التأثيرات البصرية
function initializeAnimations() {
    // تأثير العد التصاعدي للأرقام
    $('.counter').each(function() {
        const $this = $(this);
        const target = parseInt($this.text());
        
        $({ counter: 0 }).animate({ counter: target }, {
            duration: 2000,
            easing: 'swing',
            step: function() {
                $this.text(Math.ceil(this.counter));
            },
            complete: function() {
                $this.text(target);
            }
        });
    });
    
    // تأثير الظهور التدريجي للبطاقات
    $('.fade-in').each(function(i) {
        $(this).delay(i * 100).queue(function() {
            $(this).addClass('show').dequeue();
        });
    });
    
    // تأثيرات hover للبطاقات
    $('.card').hover(
        function() {
            $(this).addClass('shadow-lg-custom');
        },
        function() {
            $(this).removeClass('shadow-lg-custom');
        }
    );
    
    // تأثيرات الأزرار
    $('.btn').on('click', function() {
        $(this).addClass('pulse');
        setTimeout(() => {
            $(this).removeClass('pulse');
        }, 600);
    });
}

// دالة تهيئة الإشعارات
function initializeNotifications() {
    // تحديث عدد الإشعارات
    updateNotificationCount();
    
    // تحديث الإشعارات كل 30 ثانية
    setInterval(updateNotificationCount, 30000);
    
    // تهيئة إشعارات الوقت الفعلي
    if (typeof io !== 'undefined') {
        const socket = io();
        socket.on('notification', function(data) {
            showNotification(data.title, data.message, data.type);
            updateNotificationCount();
        });
    }
}

// دالة تحديث عدد الإشعارات
function updateNotificationCount() {
    $.ajax({
        url: 'ajax/get_notifications_count.php',
        method: 'GET',
        dataType: 'json',
        success: function(data) {
            const count = data.count || 0;
            $('#notificationCount').text(count);
            
            if (count > 0) {
                $('#notificationCount').show().addClass('pulse');
            } else {
                $('#notificationCount').hide().removeClass('pulse');
            }
        },
        error: function() {
            console.log('خطأ في تحديث الإشعارات');
        }
    });
}

// دالة عرض الإشعارات
function showNotification(title, message, type = 'info') {
    const Toast = Swal.mixin({
        toast: true,
        position: 'top-start',
        showConfirmButton: false,
        timer: 5000,
        timerProgressBar: true,
        didOpen: (toast) => {
            toast.addEventListener('mouseenter', Swal.stopTimer);
            toast.addEventListener('mouseleave', Swal.resumeTimer);
        }
    });
    
    Toast.fire({
        icon: type,
        title: title,
        text: message
    });
}

// دالة تحديث الوقت الحالي
function updateCurrentTime() {
    const now = new Date();
    const timeString = now.toLocaleString('ar-SA', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });
    $('#currentTime').text(timeString);
}

// دالة تهيئة الشريط الجانبي للهواتف المحمولة
function initializeMobileSidebar() {
    const sidebarToggle = $('#sidebarToggle');
    const sidebar = $('.sidebar');
    
    sidebarToggle.on('click', function() {
        sidebar.toggleClass('show');
    });
    
    // إغلاق الشريط الجانبي عند النقر خارجه
    $(document).on('click', function(e) {
        if (!sidebar.is(e.target) && sidebar.has(e.target).length === 0 && 
            !sidebarToggle.is(e.target) && sidebarToggle.has(e.target).length === 0) {
            sidebar.removeClass('show');
        }
    });
}

// دالة تهيئة زر العودة للأعلى
function initializeScrollToTop() {
    const scrollToTopBtn = $('.scroll-to-top');
    
    $(window).scroll(function() {
        if ($(this).scrollTop() > 300) {
            scrollToTopBtn.fadeIn();
        } else {
            scrollToTopBtn.fadeOut();
        }
    });
    
    scrollToTopBtn.on('click', function() {
        $('html, body').animate({scrollTop: 0}, 800);
    });
}

// دوال مساعدة للتفاعل مع المستخدم
function confirmDelete(message, callback) {
    Swal.fire({
        title: 'هل أنت متأكد؟',
        text: message || 'لن تتمكن من التراجع عن هذا الإجراء!',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#3085d6',
        confirmButtonText: 'نعم، احذف!',
        cancelButtonText: 'إلغاء',
        reverseButtons: true
    }).then((result) => {
        if (result.isConfirmed && callback) {
            callback();
        }
    });
}

function showSuccess(message, title = 'تم بنجاح!') {
    Swal.fire({
        icon: 'success',
        title: title,
        text: message,
        timer: 3000,
        showConfirmButton: false,
        toast: true,
        position: 'top-start'
    });
}

function showError(message, title = 'خطأ!') {
    Swal.fire({
        icon: 'error',
        title: title,
        text: message,
        confirmButtonText: 'موافق'
    });
}

function showLoading(message = 'جاري التحميل...') {
    Swal.fire({
        title: message,
        allowOutsideClick: false,
        allowEscapeKey: false,
        showConfirmButton: false,
        didOpen: () => {
            Swal.showLoading();
        }
    });
}

function hideLoading() {
    Swal.close();
}

// دالة تصدير البيانات
function exportData(format, url, filename) {
    showLoading('جاري تصدير البيانات...');
    
    $.ajax({
        url: url,
        method: 'POST',
        data: { format: format },
        xhrFields: {
            responseType: 'blob'
        },
        success: function(data) {
            hideLoading();
            
            const blob = new Blob([data]);
            const link = document.createElement('a');
            link.href = window.URL.createObjectURL(blob);
            link.download = filename;
            link.click();
            
            showSuccess('تم تصدير البيانات بنجاح');
        },
        error: function() {
            hideLoading();
            showError('حدث خطأ أثناء تصدير البيانات');
        }
    });
}

// دالة البحث المتقدم
function initializeAdvancedSearch() {
    $('#advancedSearchForm').on('submit', function(e) {
        e.preventDefault();
        
        const formData = $(this).serialize();
        const table = $('.data-table').DataTable();
        
        showLoading('جاري البحث...');
        
        $.ajax({
            url: 'ajax/advanced_search.php',
            method: 'POST',
            data: formData,
            dataType: 'json',
            success: function(data) {
                hideLoading();
                table.clear().rows.add(data).draw();
                showSuccess('تم البحث بنجاح');
            },
            error: function() {
                hideLoading();
                showError('حدث خطأ أثناء البحث');
            }
        });
    });
}

// دالة رفع الملفات
function initializeFileUpload() {
    $('.file-upload').on('change', function() {
        const file = this.files[0];
        const maxSize = 5 * 1024 * 1024; // 5MB
        
        if (file && file.size > maxSize) {
            showError('حجم الملف كبير جداً. الحد الأقصى 5 ميجابايت');
            $(this).val('');
            return;
        }
        
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                $('.file-preview').attr('src', e.target.result).show();
            };
            reader.readAsDataURL(file);
        }
    });
}

// دالة التحقق من صحة النماذج
function validateForm(formId) {
    const form = document.getElementById(formId);
    if (!form) return false;
    
    const inputs = form.querySelectorAll('input[required], select[required], textarea[required]');
    let isValid = true;
    
    inputs.forEach(input => {
        if (!input.value.trim()) {
            input.classList.add('is-invalid');
            isValid = false;
        } else {
            input.classList.remove('is-invalid');
        }
    });
    
    return isValid;
}

// دالة حفظ البيانات
function saveData(url, data, successMessage = 'تم الحفظ بنجاح') {
    showLoading('جاري الحفظ...');

    $.ajax({
        url: url,
        method: 'POST',
        data: data,
        dataType: 'json',
        success: function(response) {
            hideLoading();

            if (response.success) {
                showSuccess(successMessage);
                if (response.redirect) {
                    setTimeout(() => {
                        window.location.href = response.redirect;
                    }, 1500);
                }
            } else {
                showError(response.message || 'حدث خطأ أثناء الحفظ');
            }
        },
        error: function() {
            hideLoading();
            showError('حدث خطأ في الاتصال بالخادم');
        }
    });
}

// دالة إضافة تأثيرات بصرية للعناصر
function addVisualEffects() {
    // إضافة تأثيرات للبطاقات
    $('.card').addClass('card-animated hover-lift');

    // إضافة تأثيرات للأزرار
    $('.btn').addClass('btn-animated');

    // إضافة تأثيرات للإحصائيات
    $('.stats-card').addClass('zoom-in-effect');

    // إضافة تأثيرات للجداول
    $('.table tbody tr').addClass('slide-up-effect');

    // إضافة تأثيرات للنماذج
    $('.modal').on('show.bs.modal', function() {
        $(this).find('.modal-content').addClass('flip-in-effect');
    });

    // إضافة تأثيرات للتنبيهات
    $('.alert').addClass('slide-down-effect');

    // إضافة تأثيرات للشارات
    $('.badge').addClass('notification-badge');
}

// دالة تحريك العدادات
function animateCounters() {
    $('.counter').each(function() {
        const $this = $(this);
        const countTo = parseInt($this.text());

        $({ countNum: 0 }).animate({
            countNum: countTo
        }, {
            duration: 2000,
            easing: 'swing',
            step: function() {
                $this.text(Math.floor(this.countNum));
            },
            complete: function() {
                $this.text(countTo);
            }
        });
    });
}

// دالة إضافة تأثيرات التمرير
function addScrollEffects() {
    $(window).scroll(function() {
        const scrollTop = $(this).scrollTop();

        // إظهار/إخفاء زر العودة للأعلى
        if (scrollTop > 300) {
            $('.scroll-to-top').fadeIn();
        } else {
            $('.scroll-to-top').fadeOut();
        }

        // تأثير parallax للعناصر
        $('.parallax').each(function() {
            const speed = $(this).data('speed') || 0.5;
            const yPos = -(scrollTop * speed);
            $(this).css('transform', 'translateY(' + yPos + 'px)');
        });
    });
}

// دالة إضافة تأثيرات الكتابة
function addTypingEffect(element, text, speed = 100) {
    const $element = $(element);
    let i = 0;

    function typeWriter() {
        if (i < text.length) {
            $element.text($element.text() + text.charAt(i));
            i++;
            setTimeout(typeWriter, speed);
        }
    }

    $element.text('');
    typeWriter();
}

// دالة إضافة تأثيرات الظهور التدريجي
function addFadeInOnScroll() {
    $(window).scroll(function() {
        $('.fade-on-scroll').each(function() {
            const elementTop = $(this).offset().top;
            const elementBottom = elementTop + $(this).outerHeight();
            const viewportTop = $(window).scrollTop();
            const viewportBottom = viewportTop + $(window).height();

            if (elementBottom > viewportTop && elementTop < viewportBottom) {
                $(this).addClass('fade-in');
            }
        });
    });
}

// دالة إضافة تأثيرات التحميل المحسنة
function showLoadingOverlay(message = 'جاري التحميل...') {
    const overlay = `
        <div class="loading-overlay">
            <div class="text-center">
                <div class="loading-spinner"></div>
                <p class="mt-3 text-white">${message}</p>
            </div>
        </div>
    `;
    $('body').append(overlay);
}

function hideLoadingOverlay() {
    $('.loading-overlay').fadeOut(300, function() {
        $(this).remove();
    });
}

// دالة إضافة تأثيرات الإشعارات المحسنة
function showNotificationToast(message, type = 'info', duration = 3000) {
    const toast = `
        <div class="toast-notification toast-${type} slide-down-effect">
            <div class="toast-content">
                <i class="fas fa-${getIconForType(type)} me-2"></i>
                ${message}
            </div>
            <button class="toast-close" onclick="$(this).parent().fadeOut()">
                <i class="fas fa-times"></i>
            </button>
        </div>
    `;

    $('body').append(toast);

    setTimeout(() => {
        $('.toast-notification').last().fadeOut(300, function() {
            $(this).remove();
        });
    }, duration);
}

function getIconForType(type) {
    const icons = {
        'success': 'check-circle',
        'error': 'exclamation-circle',
        'warning': 'exclamation-triangle',
        'info': 'info-circle'
    };
    return icons[type] || 'info-circle';
}

// دالة تهيئة التأثيرات المتقدمة
function initializeAdvancedEffects() {
    // إضافة التأثيرات البصرية
    addVisualEffects();

    // تحريك العدادات
    animateCounters();

    // إضافة تأثيرات التمرير
    addScrollEffects();

    // إضافة تأثيرات الظهور التدريجي
    addFadeInOnScroll();

    // تأثيرات خاصة للنماذج
    $('form').on('submit', function() {
        $(this).find('.btn[type="submit"]').addClass('glow-effect');
    });

    // تأثيرات للروابط
    $('a').hover(
        function() { $(this).addClass('hover-glow'); },
        function() { $(this).removeClass('hover-glow'); }
    );

    // تأثيرات للقوائم المنسدلة
    $('.dropdown-toggle').on('click', function() {
        $(this).next('.dropdown-menu').addClass('slide-down-effect');
    });
}

// تهيئة التأثيرات المتقدمة عند تحميل الصفحة
$(document).ready(function() {
    setTimeout(initializeAdvancedEffects, 500);
});
