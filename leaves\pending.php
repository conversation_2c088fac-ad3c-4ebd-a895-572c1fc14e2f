<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit();
}

$page_title = 'الطلبات المعلقة';
$success_message = '';
$error_message = '';

try {
    $pdo = getConnection();
    
    // الحصول على الطلبات المعلقة
    $pending_stmt = $pdo->prepare("
        SELECT 
            lr.*,
            e.employee_number,
            e.first_name,
            e.last_name,
            lt.name as leave_type_name,
            lt.color as leave_type_color,
            d.name as department_name,
            requester.first_name as requester_first_name,
            requester.last_name as requester_last_name
        FROM leave_requests lr
        JOIN employees e ON lr.employee_id = e.id
        JOIN leave_types lt ON lr.leave_type_id = lt.id
        LEFT JOIN departments d ON e.department_id = d.id
        LEFT JOIN users u ON lr.requested_by = u.id
        LEFT JOIN employees requester ON u.id = requester.user_id
        WHERE lr.status = 'pending'
        ORDER BY lr.created_at ASC
    ");
    $pending_stmt->execute();
    $pending_requests = $pending_stmt->fetchAll();
    
    // الحصول على طلبات الموظف الحالي إذا كان موظفاً عادياً
    $my_requests = [];
    if ($_SESSION['role'] == 'employee') {
        $my_stmt = $pdo->prepare("
            SELECT 
                lr.*,
                lt.name as leave_type_name,
                lt.color as leave_type_color
            FROM leave_requests lr
            JOIN leave_types lt ON lr.leave_type_id = lt.id
            JOIN employees e ON lr.employee_id = e.id
            WHERE e.user_id = ?
            ORDER BY lr.created_at DESC
            LIMIT 10
        ");
        $my_stmt->execute([$_SESSION['user_id']]);
        $my_requests = $my_stmt->fetchAll();
    }
    
} catch (PDOException $e) {
    $pending_requests = [];
    $my_requests = [];
}

// معالجة الموافقة/الرفض
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action'])) {
    try {
        $request_id = (int)$_POST['request_id'];
        $action = $_POST['action']; // approve أو reject
        $comments = sanitizeInput($_POST['comments'] ?? '');
        
        if (!in_array($action, ['approve', 'reject'])) {
            throw new Exception('إجراء غير صحيح');
        }
        
        // التحقق من صلاحية المستخدم
        if (!in_array($_SESSION['role'], ['manager', 'hr', 'admin'])) {
            throw new Exception('ليس لديك صلاحية للموافقة على الطلبات');
        }
        
        // الحصول على تفاصيل الطلب
        $request_stmt = $pdo->prepare("
            SELECT lr.*, e.first_name, e.last_name, lt.name as leave_type_name
            FROM leave_requests lr
            JOIN employees e ON lr.employee_id = e.id
            JOIN leave_types lt ON lr.leave_type_id = lt.id
            WHERE lr.id = ? AND lr.status = 'pending'
        ");
        $request_stmt->execute([$request_id]);
        $request = $request_stmt->fetch();
        
        if (!$request) {
            throw new Exception('الطلب غير موجود أو تم معالجته مسبقاً');
        }
        
        $new_status = $action == 'approve' ? 'approved' : 'rejected';
        
        // بدء المعاملة
        $pdo->beginTransaction();
        
        try {
            // تحديث حالة الطلب
            $update_stmt = $pdo->prepare("
                UPDATE leave_requests 
                SET status = ?, approved_by = ?, approved_at = NOW(), comments = ?
                WHERE id = ?
            ");
            $update_stmt->execute([$new_status, $_SESSION['user_id'], $comments, $request_id]);
            
            if ($action == 'approve') {
                // خصم الأيام من الرصيد
                $balance_stmt = $pdo->prepare("
                    UPDATE leave_balances 
                    SET used_days = used_days + ?, remaining_days = remaining_days - ?
                    WHERE employee_id = ? AND leave_type_id = ? AND year = YEAR(CURDATE())
                ");
                $balance_stmt->execute([
                    $request['days_requested'], 
                    $request['days_requested'],
                    $request['employee_id'], 
                    $request['leave_type_id']
                ]);
                
                // إضافة سجلات الحضور للأيام المعتمدة
                $current_date = new DateTime($request['start_date']);
                $end_date = new DateTime($request['end_date']);
                
                while ($current_date <= $end_date) {
                    // تجاهل أيام الجمعة والسبت (عطلة نهاية الأسبوع)
                    $day_of_week = $current_date->format('N');
                    if ($day_of_week < 6) { // من الأحد إلى الخميس
                        $attendance_stmt = $pdo->prepare("
                            INSERT INTO attendance (employee_id, date, status, created_at)
                            VALUES (?, ?, 'إجازة', NOW())
                            ON DUPLICATE KEY UPDATE status = 'إجازة'
                        ");
                        $attendance_stmt->execute([
                            $request['employee_id'], 
                            $current_date->format('Y-m-d')
                        ]);
                    }
                    $current_date->modify('+1 day');
                }
            }
            
            // إرسال إشعار للموظف
            $employee_user_stmt = $pdo->prepare("
                SELECT user_id FROM employees WHERE id = ?
            ");
            $employee_user_stmt->execute([$request['employee_id']]);
            $employee_user = $employee_user_stmt->fetch();
            
            if ($employee_user) {
                $notification_title = $action == 'approve' ? 'تم قبول طلب الإجازة' : 'تم رفض طلب الإجازة';
                $notification_message = $action == 'approve' 
                    ? "تم قبول طلب إجازة {$request['leave_type_name']} من {$request['start_date']} إلى {$request['end_date']}"
                    : "تم رفض طلب إجازة {$request['leave_type_name']} من {$request['start_date']} إلى {$request['end_date']}";
                
                sendNotification(
                    $employee_user['user_id'],
                    $notification_title,
                    $notification_message,
                    $action == 'approve' ? 'success' : 'warning'
                );
            }
            
            // تأكيد المعاملة
            $pdo->commit();
            
            // تسجيل النشاط
            $action_text = $action == 'approve' ? 'موافقة على إجازة' : 'رفض إجازة';
            logActivity(
                $_SESSION['user_id'], 
                $action_text, 
                "الموظف: {$request['first_name']} {$request['last_name']} - النوع: {$request['leave_type_name']}"
            );
            
            $success_message = $action == 'approve' 
                ? 'تم قبول طلب الإجازة بنجاح' 
                : 'تم رفض طلب الإجازة';
            
            // إعادة تحميل الصفحة لتحديث القائمة
            header('Location: pending.php?success=' . urlencode($success_message));
            exit();
            
        } catch (Exception $e) {
            $pdo->rollback();
            throw $e;
        }
        
    } catch (Exception $e) {
        $error_message = $e->getMessage();
    }
}

// رسالة النجاح من إعادة التوجيه
if (isset($_GET['success'])) {
    $success_message = $_GET['success'];
}

include '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
            <div class="position-sticky pt-3">
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link" href="../index.php">
                            <i class="fas fa-tachometer-alt me-2"></i>
                            لوحة التحكم
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../employees/list.php">
                            <i class="fas fa-users me-2"></i>
                            قائمة الموظفين
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="request.php">
                            <i class="fas fa-paper-plane me-2"></i>
                            طلب إجازة
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="pending.php">
                            <i class="fas fa-hourglass-half me-2"></i>
                            الطلبات المعلقة
                            <?php if (count($pending_requests) > 0): ?>
                                <span class="badge bg-warning"><?php echo count($pending_requests); ?></span>
                            <?php endif; ?>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="balance.php">
                            <i class="fas fa-balance-scale me-2"></i>
                            أرصدة الإجازات
                        </a>
                    </li>
                </ul>
            </div>
        </nav>
        
        <!-- Main Content -->
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">
                    <i class="fas fa-hourglass-half me-2"></i>
                    الطلبات المعلقة
                    <?php if (count($pending_requests) > 0): ?>
                        <span class="badge bg-warning"><?php echo count($pending_requests); ?></span>
                    <?php endif; ?>
                </h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <a href="request.php" class="btn btn-primary">
                        <i class="fas fa-plus me-1"></i>
                        طلب إجازة جديدة
                    </a>
                </div>
            </div>

            <?php if ($success_message): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle me-2"></i>
                    <?php echo $success_message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?php if ($error_message): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <?php echo $error_message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?php if (in_array($_SESSION['role'], ['manager', 'hr', 'admin'])): ?>
            <!-- الطلبات المعلقة للموافقة -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-clipboard-list me-2"></i>
                        طلبات تحتاج موافقة (<?php echo count($pending_requests); ?>)
                    </h6>
                </div>
                <div class="card-body">
                    <?php if (empty($pending_requests)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                            <h5>لا توجد طلبات معلقة</h5>
                            <p class="text-muted">جميع الطلبات تم معالجتها</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-bordered data-table" id="pendingRequestsTable" width="100%" cellspacing="0">
                                <thead>
                                    <tr>
                                        <th>الموظف</th>
                                        <th>نوع الإجازة</th>
                                        <th>من تاريخ</th>
                                        <th>إلى تاريخ</th>
                                        <th>عدد الأيام</th>
                                        <th>السبب</th>
                                        <th>تاريخ الطلب</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($pending_requests as $request): ?>
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="avatar-sm bg-primary rounded-circle d-flex align-items-center justify-content-center me-2">
                                                    <i class="fas fa-user text-white"></i>
                                                </div>
                                                <div>
                                                    <strong><?php echo htmlspecialchars($request['first_name'] . ' ' . $request['last_name']); ?></strong>
                                                    <br><small class="text-muted"><?php echo htmlspecialchars($request['employee_number']); ?></small>
                                                    <?php if ($request['department_name']): ?>
                                                        <br><small class="text-muted"><?php echo htmlspecialchars($request['department_name']); ?></small>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge" style="background-color: <?php echo $request['leave_type_color'] ?: '#6c757d'; ?>">
                                                <?php echo htmlspecialchars($request['leave_type_name']); ?>
                                            </span>
                                        </td>
                                        <td><?php echo formatDate($request['start_date']); ?></td>
                                        <td><?php echo formatDate($request['end_date']); ?></td>
                                        <td>
                                            <span class="badge bg-info">
                                                <?php echo $request['days_requested']; ?> يوم
                                            </span>
                                        </td>
                                        <td>
                                            <span class="text-truncate d-inline-block" style="max-width: 150px;"
                                                  title="<?php echo htmlspecialchars($request['reason']); ?>">
                                                <?php echo htmlspecialchars(substr($request['reason'], 0, 50)) . (strlen($request['reason']) > 50 ? '...' : ''); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <?php echo formatDateTime($request['created_at']); ?>
                                            <?php if ($request['requester_first_name']): ?>
                                                <br><small class="text-muted">
                                                    بواسطة: <?php echo htmlspecialchars($request['requester_first_name'] . ' ' . $request['requester_last_name']); ?>
                                                </small>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <button type="button" class="btn btn-success btn-sm" 
                                                        onclick="showApprovalModal(<?php echo $request['id']; ?>, 'approve', '<?php echo htmlspecialchars($request['first_name'] . ' ' . $request['last_name']); ?>')">
                                                    <i class="fas fa-check"></i>
                                                    موافقة
                                                </button>
                                                <button type="button" class="btn btn-danger btn-sm" 
                                                        onclick="showApprovalModal(<?php echo $request['id']; ?>, 'reject', '<?php echo htmlspecialchars($request['first_name'] . ' ' . $request['last_name']); ?>')">
                                                    <i class="fas fa-times"></i>
                                                    رفض
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
            <?php endif; ?>

            <?php if ($_SESSION['role'] == 'employee' && !empty($my_requests)): ?>
            <!-- طلبات الموظف الحالي -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-user-clock me-2"></i>
                        طلباتي الأخيرة
                    </h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered" width="100%" cellspacing="0">
                            <thead>
                                <tr>
                                    <th>نوع الإجازة</th>
                                    <th>من تاريخ</th>
                                    <th>إلى تاريخ</th>
                                    <th>عدد الأيام</th>
                                    <th>الحالة</th>
                                    <th>تاريخ الطلب</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($my_requests as $request): ?>
                                <tr>
                                    <td>
                                        <span class="badge" style="background-color: <?php echo $request['leave_type_color'] ?: '#6c757d'; ?>">
                                            <?php echo htmlspecialchars($request['leave_type_name']); ?>
                                        </span>
                                    </td>
                                    <td><?php echo formatDate($request['start_date']); ?></td>
                                    <td><?php echo formatDate($request['end_date']); ?></td>
                                    <td>
                                        <span class="badge bg-info">
                                            <?php echo $request['days_requested']; ?> يوم
                                        </span>
                                    </td>
                                    <td>
                                        <?php
                                        $status_classes = [
                                            'pending' => 'warning',
                                            'approved' => 'success',
                                            'rejected' => 'danger'
                                        ];
                                        $status_text = [
                                            'pending' => 'في الانتظار',
                                            'approved' => 'مقبول',
                                            'rejected' => 'مرفوض'
                                        ];
                                        $status_class = $status_classes[$request['status']] ?? 'secondary';
                                        ?>
                                        <span class="badge bg-<?php echo $status_class; ?>">
                                            <?php echo $status_text[$request['status']] ?? $request['status']; ?>
                                        </span>
                                    </td>
                                    <td><?php echo formatDateTime($request['created_at']); ?></td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <?php endif; ?>
        </main>
    </div>
</div>

<!-- Modal للموافقة/الرفض -->
<div class="modal fade" id="approvalModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="approvalModalTitle"></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" id="approvalForm">
                <div class="modal-body">
                    <input type="hidden" name="request_id" id="modal_request_id">
                    <input type="hidden" name="action" id="modal_action">
                    
                    <div class="mb-3">
                        <label for="modal_comments" class="form-label">تعليقات (اختياري)</label>
                        <textarea class="form-control" id="modal_comments" name="comments" rows="3" 
                                  placeholder="أضف تعليقاً على القرار..."></textarea>
                    </div>
                    
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <span id="modal_confirmation_text"></span>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn" id="modal_submit_btn">تأكيد</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// عرض modal للموافقة/الرفض
function showApprovalModal(requestId, action, employeeName) {
    document.getElementById('modal_request_id').value = requestId;
    document.getElementById('modal_action').value = action;
    
    const modal = document.getElementById('approvalModal');
    const title = document.getElementById('approvalModalTitle');
    const confirmationText = document.getElementById('modal_confirmation_text');
    const submitBtn = document.getElementById('modal_submit_btn');
    
    if (action === 'approve') {
        title.textContent = 'موافقة على طلب الإجازة';
        confirmationText.textContent = `هل تريد الموافقة على طلب إجازة الموظف: ${employeeName}؟`;
        submitBtn.className = 'btn btn-success';
        submitBtn.innerHTML = '<i class="fas fa-check me-1"></i>موافقة';
    } else {
        title.textContent = 'رفض طلب الإجازة';
        confirmationText.textContent = `هل تريد رفض طلب إجازة الموظف: ${employeeName}؟`;
        submitBtn.className = 'btn btn-danger';
        submitBtn.innerHTML = '<i class="fas fa-times me-1"></i>رفض';
    }
    
    // إعادة تعيين التعليقات
    document.getElementById('modal_comments').value = '';
    
    // عرض المودال
    new bootstrap.Modal(modal).show();
}

// تهيئة DataTable
$(document).ready(function() {
    $('#pendingRequestsTable').DataTable({
        language: {
            url: 'https://cdn.datatables.net/plug-ins/1.13.6/i18n/ar.json'
        },
        responsive: true,
        pageLength: 25,
        order: [[6, 'asc']], // ترتيب حسب تاريخ الطلب
        columnDefs: [
            { orderable: false, targets: [7] } // عمود الإجراءات غير قابل للترتيب
        ]
    });
});
</script>

<?php include '../includes/footer.php'; ?>
