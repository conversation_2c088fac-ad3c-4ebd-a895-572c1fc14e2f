<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit();
}

$page_title = 'أرصدة الإجازات';

// معالجة فلاتر البحث
$year = $_GET['year'] ?? date('Y');
$employee_id = $_GET['employee_id'] ?? '';

try {
    $pdo = getConnection();
    
    // الحصول على قائمة الموظفين
    $employees_stmt = $pdo->query("
        SELECT id, employee_number, first_name, last_name 
        FROM employees 
        WHERE status = 'active' 
        ORDER BY first_name, last_name
    ");
    $employees = $employees_stmt->fetchAll();
    
    // الحصول على أنواع الإجازات
    $leave_types_stmt = $pdo->query("
        SELECT * FROM leave_types 
        WHERE status = 'active' 
        ORDER BY name
    ");
    $leave_types = $leave_types_stmt->fetchAll();
    
    // بناء استعلام الأرصدة
    $where_conditions = ["lb.year = ?"];
    $params = [$year];
    
    if ($employee_id) {
        $where_conditions[] = "lb.employee_id = ?";
        $params[] = $employee_id;
    }
    
    $where_clause = implode(' AND ', $where_conditions);
    
    // الحصول على أرصدة الإجازات
    $balances_stmt = $pdo->prepare("
        SELECT 
            lb.*,
            e.employee_number,
            e.first_name,
            e.last_name,
            lt.name as leave_type_name,
            lt.color as leave_type_color,
            d.name as department_name
        FROM leave_balances lb
        JOIN employees e ON lb.employee_id = e.id
        JOIN leave_types lt ON lb.leave_type_id = lt.id
        LEFT JOIN departments d ON e.department_id = d.id
        WHERE $where_clause
        ORDER BY e.first_name, e.last_name, lt.name
    ");
    $balances_stmt->execute($params);
    $balances = $balances_stmt->fetchAll();
    
    // تجميع البيانات حسب الموظف
    $employees_balances = [];
    foreach ($balances as $balance) {
        $emp_key = $balance['employee_id'];
        if (!isset($employees_balances[$emp_key])) {
            $employees_balances[$emp_key] = [
                'employee' => [
                    'id' => $balance['employee_id'],
                    'number' => $balance['employee_number'],
                    'name' => $balance['first_name'] . ' ' . $balance['last_name'],
                    'department' => $balance['department_name']
                ],
                'balances' => []
            ];
        }
        $employees_balances[$emp_key]['balances'][] = $balance;
    }
    
    // حساب الإحصائيات
    $total_allocated = array_sum(array_column($balances, 'total_days'));
    $total_used = array_sum(array_column($balances, 'used_days'));
    $total_remaining = array_sum(array_column($balances, 'remaining_days'));
    
} catch (PDOException $e) {
    $employees = [];
    $leave_types = [];
    $employees_balances = [];
    $balances = [];
}

include '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
            <div class="position-sticky pt-3">
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link" href="../index.php">
                            <i class="fas fa-tachometer-alt me-2"></i>
                            لوحة التحكم
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../employees/list.php">
                            <i class="fas fa-users me-2"></i>
                            قائمة الموظفين
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="request.php">
                            <i class="fas fa-paper-plane me-2"></i>
                            طلب إجازة
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="pending.php">
                            <i class="fas fa-hourglass-half me-2"></i>
                            الطلبات المعلقة
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="balance.php">
                            <i class="fas fa-balance-scale me-2"></i>
                            أرصدة الإجازات
                        </a>
                    </li>
                </ul>
            </div>
        </nav>
        
        <!-- Main Content -->
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">
                    <i class="fas fa-balance-scale me-2"></i>
                    أرصدة الإجازات
                </h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <button type="button" class="btn btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
                            <i class="fas fa-download me-1"></i>
                            تصدير التقرير
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" onclick="exportBalances('excel')">
                                <i class="fas fa-file-excel me-2"></i>Excel
                            </a></li>
                            <li><a class="dropdown-item" href="#" onclick="exportBalances('pdf')">
                                <i class="fas fa-file-pdf me-2"></i>PDF
                            </a></li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- فلاتر التقرير -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-filter me-2"></i>
                        فلاتر التقرير
                    </h6>
                </div>
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-4">
                            <label for="year" class="form-label">السنة</label>
                            <select class="form-select" id="year" name="year">
                                <?php for ($y = date('Y') - 2; $y <= date('Y') + 1; $y++): ?>
                                    <option value="<?php echo $y; ?>" <?php echo $year == $y ? 'selected' : ''; ?>>
                                        <?php echo $y; ?>
                                    </option>
                                <?php endfor; ?>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label for="employee_id" class="form-label">الموظف</label>
                            <select class="form-select" id="employee_id" name="employee_id">
                                <option value="">جميع الموظفين</option>
                                <?php foreach ($employees as $emp): ?>
                                    <option value="<?php echo $emp['id']; ?>" 
                                            <?php echo $employee_id == $emp['id'] ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($emp['employee_number'] . ' - ' . $emp['first_name'] . ' ' . $emp['last_name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-4 d-flex align-items-end">
                            <button type="submit" class="btn btn-primary me-2">
                                <i class="fas fa-search me-1"></i>
                                تطبيق الفلاتر
                            </button>
                            <a href="balance.php" class="btn btn-outline-secondary">
                                <i class="fas fa-undo me-1"></i>
                                إعادة تعيين
                            </a>
                        </div>
                    </form>
                </div>
            </div>

            <!-- إحصائيات عامة -->
            <div class="row mb-4">
                <div class="col-xl-4 col-md-6 mb-4">
                    <div class="card stats-card border-0 shadow-sm">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                        إجمالي الأيام المخصصة
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800 counter">
                                        <?php echo $total_allocated; ?>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-calendar-plus fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-4 col-md-6 mb-4">
                    <div class="card stats-card warning border-0 shadow-sm">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                        الأيام المستخدمة
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800 counter">
                                        <?php echo $total_used; ?>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-calendar-check fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-4 col-md-6 mb-4">
                    <div class="card stats-card success border-0 shadow-sm">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                        الأيام المتبقية
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800 counter">
                                        <?php echo $total_remaining; ?>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-calendar-minus fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- أرصدة الموظفين -->
            <?php if (empty($employees_balances)): ?>
                <div class="card shadow mb-4">
                    <div class="card-body text-center py-5">
                        <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                        <h5>لا توجد أرصدة إجازات</h5>
                        <p class="text-muted">لم يتم العثور على أرصدة إجازات للمعايير المحددة</p>
                    </div>
                </div>
            <?php else: ?>
                <?php foreach ($employees_balances as $emp_data): ?>
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <h6 class="m-0 font-weight-bold text-primary">
                                <i class="fas fa-user me-2"></i>
                                <?php echo htmlspecialchars($emp_data['employee']['name']); ?>
                                <small class="text-muted">(<?php echo htmlspecialchars($emp_data['employee']['number']); ?>)</small>
                            </h6>
                            <?php if ($emp_data['employee']['department']): ?>
                                <span class="badge bg-secondary">
                                    <?php echo htmlspecialchars($emp_data['employee']['department']); ?>
                                </span>
                            <?php endif; ?>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <?php foreach ($emp_data['balances'] as $balance): ?>
                            <div class="col-lg-4 col-md-6 mb-3">
                                <div class="card border-0 bg-light">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between align-items-center mb-2">
                                            <h6 class="card-title mb-0">
                                                <span class="badge" style="background-color: <?php echo $balance['leave_type_color'] ?: '#6c757d'; ?>">
                                                    <?php echo htmlspecialchars($balance['leave_type_name']); ?>
                                                </span>
                                            </h6>
                                        </div>
                                        
                                        <div class="progress mb-2" style="height: 8px;">
                                            <?php 
                                            $usage_percentage = $balance['total_days'] > 0 ? ($balance['used_days'] / $balance['total_days']) * 100 : 0;
                                            $progress_class = $usage_percentage > 80 ? 'bg-danger' : ($usage_percentage > 60 ? 'bg-warning' : 'bg-success');
                                            ?>
                                            <div class="progress-bar <?php echo $progress_class; ?>" 
                                                 style="width: <?php echo $usage_percentage; ?>%"></div>
                                        </div>
                                        
                                        <div class="row text-center small">
                                            <div class="col-4">
                                                <strong class="text-primary"><?php echo $balance['total_days']; ?></strong>
                                                <br><span class="text-muted">المخصص</span>
                                            </div>
                                            <div class="col-4">
                                                <strong class="text-warning"><?php echo $balance['used_days']; ?></strong>
                                                <br><span class="text-muted">المستخدم</span>
                                            </div>
                                            <div class="col-4">
                                                <strong class="text-success"><?php echo $balance['remaining_days']; ?></strong>
                                                <br><span class="text-muted">المتبقي</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            <?php endif; ?>

            <!-- جدول تفصيلي -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-table me-2"></i>
                        جدول تفصيلي لأرصدة الإجازات
                    </h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered data-table" id="balancesTable" width="100%" cellspacing="0">
                            <thead>
                                <tr>
                                    <th>الموظف</th>
                                    <th>القسم</th>
                                    <th>نوع الإجازة</th>
                                    <th>المخصص</th>
                                    <th>المستخدم</th>
                                    <th>المتبقي</th>
                                    <th>نسبة الاستخدام</th>
                                    <th>آخر تحديث</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($balances as $balance): ?>
                                <tr>
                                    <td>
                                        <div>
                                            <strong><?php echo htmlspecialchars($balance['first_name'] . ' ' . $balance['last_name']); ?></strong>
                                            <br><small class="text-muted"><?php echo htmlspecialchars($balance['employee_number']); ?></small>
                                        </div>
                                    </td>
                                    <td><?php echo htmlspecialchars($balance['department_name'] ?: 'غير محدد'); ?></td>
                                    <td>
                                        <span class="badge" style="background-color: <?php echo $balance['leave_type_color'] ?: '#6c757d'; ?>">
                                            <?php echo htmlspecialchars($balance['leave_type_name']); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-primary">
                                            <?php echo $balance['total_days']; ?> يوم
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-warning">
                                            <?php echo $balance['used_days']; ?> يوم
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-success">
                                            <?php echo $balance['remaining_days']; ?> يوم
                                        </span>
                                    </td>
                                    <td>
                                        <?php 
                                        $usage_percentage = $balance['total_days'] > 0 ? ($balance['used_days'] / $balance['total_days']) * 100 : 0;
                                        $progress_class = $usage_percentage > 80 ? 'bg-danger' : ($usage_percentage > 60 ? 'bg-warning' : 'bg-success');
                                        ?>
                                        <div class="progress" style="height: 20px;">
                                            <div class="progress-bar <?php echo $progress_class; ?>" 
                                                 style="width: <?php echo $usage_percentage; ?>%">
                                                <?php echo number_format($usage_percentage, 1); ?>%
                                            </div>
                                        </div>
                                    </td>
                                    <td><?php echo formatDateTime($balance['updated_at']); ?></td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<script>
// تصدير أرصدة الإجازات
function exportBalances(format) {
    const params = new URLSearchParams(window.location.search);
    params.set('export', format);
    
    showLoading('جاري تصدير التقرير...');
    
    fetch('export_balances.php?' + params.toString())
        .then(response => response.blob())
        .then(blob => {
            hideLoading();
            
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `leave_balances_${new Date().toISOString().split('T')[0]}.${format}`;
            a.click();
            window.URL.revokeObjectURL(url);
            
            showSuccess('تم تصدير التقرير بنجاح');
        })
        .catch(error => {
            hideLoading();
            showError('حدث خطأ أثناء تصدير التقرير');
        });
}

// تهيئة DataTable
$(document).ready(function() {
    $('#balancesTable').DataTable({
        language: {
            url: 'https://cdn.datatables.net/plug-ins/1.13.6/i18n/ar.json'
        },
        responsive: true,
        pageLength: 25,
        order: [[0, 'asc']],
        columnDefs: [
            { orderable: false, targets: [6] } // عمود نسبة الاستخدام غير قابل للترتيب
        ]
    });
});
</script>

<?php include '../includes/footer.php'; ?>
