<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// التحقق من تسجيل الدخول والصلاحيات (المدير فقط)
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header('Location: ../login.php');
    exit();
}

$page_title = 'إدارة المستخدمين';
$success_message = '';
$error_message = '';

try {
    $pdo = getConnection();
    
    // الحصول على قائمة المستخدمين
    $users_stmt = $pdo->query("
        SELECT 
            u.*,
            e.first_name, e.last_name, e.employee_number,
            d.name as department_name
        FROM users u
        LEFT JOIN employees e ON u.employee_id = e.id
        LEFT JOIN departments d ON e.department_id = d.id
        ORDER BY u.created_at DESC
    ");
    $users = $users_stmt->fetchAll();
    
    // الحصول على قائمة الموظفين الذين ليس لديهم حسابات مستخدمين
    $employees_stmt = $pdo->query("
        SELECT e.id, e.first_name, e.last_name, e.employee_number, d.name as department_name
        FROM employees e
        LEFT JOIN departments d ON e.department_id = d.id
        LEFT JOIN users u ON e.id = u.employee_id
        WHERE u.id IS NULL AND e.status = 'active'
        ORDER BY e.first_name, e.last_name
    ");
    $available_employees = $employees_stmt->fetchAll();
    
} catch (PDOException $e) {
    $users = [];
    $available_employees = [];
}

// معالجة إضافة/تعديل مستخدم
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    try {
        $pdo = getConnection();
        
        $action = $_POST['action'];
        $username = sanitizeInput($_POST['username']);
        $email = sanitizeInput($_POST['email']);
        $role = $_POST['role'];
        $employee_id = $_POST['employee_id'] ? (int)$_POST['employee_id'] : null;
        $status = $_POST['status'];
        
        if (empty($username) || empty($email)) {
            throw new Exception('يرجى ملء جميع الحقول المطلوبة');
        }
        
        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            throw new Exception('يرجى إدخال بريد إلكتروني صحيح');
        }
        
        if ($action == 'add') {
            $password = $_POST['password'];
            $confirm_password = $_POST['confirm_password'];
            
            if (empty($password)) {
                throw new Exception('يرجى إدخال كلمة المرور');
            }
            
            if ($password !== $confirm_password) {
                throw new Exception('كلمة المرور وتأكيدها غير متطابقتين');
            }
            
            if (strlen($password) < 6) {
                throw new Exception('كلمة المرور يجب أن تكون 6 أحرف على الأقل');
            }
            
            // التحقق من عدم تكرار اسم المستخدم
            $check_stmt = $pdo->prepare("SELECT COUNT(*) FROM users WHERE username = ?");
            $check_stmt->execute([$username]);
            if ($check_stmt->fetchColumn() > 0) {
                throw new Exception('اسم المستخدم موجود مسبقاً');
            }
            
            // التحقق من عدم تكرار البريد الإلكتروني
            $check_stmt = $pdo->prepare("SELECT COUNT(*) FROM users WHERE email = ?");
            $check_stmt->execute([$email]);
            if ($check_stmt->fetchColumn() > 0) {
                throw new Exception('البريد الإلكتروني موجود مسبقاً');
            }
            
            // التحقق من عدم ربط الموظف بحساب آخر
            if ($employee_id) {
                $check_stmt = $pdo->prepare("SELECT COUNT(*) FROM users WHERE employee_id = ?");
                $check_stmt->execute([$employee_id]);
                if ($check_stmt->fetchColumn() > 0) {
                    throw new Exception('هذا الموظف مرتبط بحساب مستخدم آخر');
                }
            }
            
            // إضافة مستخدم جديد
            $hashed_password = password_hash($password, PASSWORD_DEFAULT);
            $insert_stmt = $pdo->prepare("
                INSERT INTO users (username, email, password, role, employee_id, status, created_at)
                VALUES (?, ?, ?, ?, ?, ?, NOW())
            ");
            $insert_stmt->execute([$username, $email, $hashed_password, $role, $employee_id, $status]);
            
            logActivity($_SESSION['user_id'], 'إضافة مستخدم', "تم إضافة المستخدم: $username");
            $success_message = 'تم إضافة المستخدم بنجاح';
            
        } elseif ($action == 'edit') {
            $user_id = (int)$_POST['user_id'];
            
            // التحقق من عدم تكرار اسم المستخدم (باستثناء المستخدم الحالي)
            $check_stmt = $pdo->prepare("SELECT COUNT(*) FROM users WHERE username = ? AND id != ?");
            $check_stmt->execute([$username, $user_id]);
            if ($check_stmt->fetchColumn() > 0) {
                throw new Exception('اسم المستخدم موجود مسبقاً');
            }
            
            // التحقق من عدم تكرار البريد الإلكتروني (باستثناء المستخدم الحالي)
            $check_stmt = $pdo->prepare("SELECT COUNT(*) FROM users WHERE email = ? AND id != ?");
            $check_stmt->execute([$email, $user_id]);
            if ($check_stmt->fetchColumn() > 0) {
                throw new Exception('البريد الإلكتروني موجود مسبقاً');
            }
            
            // التحقق من عدم ربط الموظف بحساب آخر (باستثناء المستخدم الحالي)
            if ($employee_id) {
                $check_stmt = $pdo->prepare("SELECT COUNT(*) FROM users WHERE employee_id = ? AND id != ?");
                $check_stmt->execute([$employee_id, $user_id]);
                if ($check_stmt->fetchColumn() > 0) {
                    throw new Exception('هذا الموظف مرتبط بحساب مستخدم آخر');
                }
            }
            
            // تحديث المستخدم
            $update_stmt = $pdo->prepare("
                UPDATE users 
                SET username = ?, email = ?, role = ?, employee_id = ?, status = ?, updated_at = NOW()
                WHERE id = ?
            ");
            $update_stmt->execute([$username, $email, $role, $employee_id, $status, $user_id]);
            
            // تحديث كلمة المرور إذا تم إدخالها
            if (!empty($_POST['password'])) {
                $password = $_POST['password'];
                $confirm_password = $_POST['confirm_password'];
                
                if ($password !== $confirm_password) {
                    throw new Exception('كلمة المرور وتأكيدها غير متطابقتين');
                }
                
                if (strlen($password) < 6) {
                    throw new Exception('كلمة المرور يجب أن تكون 6 أحرف على الأقل');
                }
                
                $hashed_password = password_hash($password, PASSWORD_DEFAULT);
                $password_stmt = $pdo->prepare("UPDATE users SET password = ? WHERE id = ?");
                $password_stmt->execute([$hashed_password, $user_id]);
            }
            
            logActivity($_SESSION['user_id'], 'تعديل مستخدم', "تم تعديل المستخدم: $username");
            $success_message = 'تم تحديث المستخدم بنجاح';
        }
        
        // إعادة تحميل المستخدمين
        $users_stmt = $pdo->query("
            SELECT 
                u.*,
                e.first_name, e.last_name, e.employee_number,
                d.name as department_name
            FROM users u
            LEFT JOIN employees e ON u.employee_id = e.id
            LEFT JOIN departments d ON e.department_id = d.id
            ORDER BY u.created_at DESC
        ");
        $users = $users_stmt->fetchAll();
        
        // إعادة تحميل الموظفين المتاحين
        $employees_stmt = $pdo->query("
            SELECT e.id, e.first_name, e.last_name, e.employee_number, d.name as department_name
            FROM employees e
            LEFT JOIN departments d ON e.department_id = d.id
            LEFT JOIN users u ON e.id = u.employee_id
            WHERE u.id IS NULL AND e.status = 'active'
            ORDER BY e.first_name, e.last_name
        ");
        $available_employees = $employees_stmt->fetchAll();
        
    } catch (Exception $e) {
        $error_message = $e->getMessage();
    }
}

// معالجة حذف مستخدم
if (isset($_GET['delete']) && is_numeric($_GET['delete'])) {
    try {
        $pdo = getConnection();
        $user_id = (int)$_GET['delete'];
        
        // التأكد من عدم حذف المستخدم الحالي
        if ($user_id == $_SESSION['user_id']) {
            throw new Exception('لا يمكنك حذف حسابك الخاص');
        }
        
        // حذف المستخدم
        $delete_stmt = $pdo->prepare("DELETE FROM users WHERE id = ?");
        $delete_stmt->execute([$user_id]);
        
        logActivity($_SESSION['user_id'], 'حذف مستخدم', "تم حذف مستخدم برقم: $user_id");
        $success_message = 'تم حذف المستخدم بنجاح';
        
        // إعادة توجيه لتجنب إعادة الحذف
        header('Location: users.php?success=' . urlencode($success_message));
        exit();
        
    } catch (Exception $e) {
        $error_message = $e->getMessage();
    }
}

// رسالة النجاح من إعادة التوجيه
if (isset($_GET['success'])) {
    $success_message = $_GET['success'];
}

include '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
            <div class="position-sticky pt-3">
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link" href="../index.php">
                            <i class="fas fa-tachometer-alt me-2"></i>
                            لوحة التحكم
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="company.php">
                            <i class="fas fa-building me-2"></i>
                            إعدادات الشركة
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="departments.php">
                            <i class="fas fa-sitemap me-2"></i>
                            إدارة الأقسام
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="positions.php">
                            <i class="fas fa-user-tie me-2"></i>
                            المسميات الوظيفية
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="leave_types.php">
                            <i class="fas fa-calendar-alt me-2"></i>
                            أنواع الإجازات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="users.php">
                            <i class="fas fa-users-cog me-2"></i>
                            إدارة المستخدمين
                        </a>
                    </li>
                </ul>
            </div>
        </nav>
        
        <!-- Main Content -->
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">
                    <i class="fas fa-users-cog me-2"></i>
                    إدارة المستخدمين
                </h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#userModal" onclick="openAddModal()">
                        <i class="fas fa-plus me-1"></i>
                        إضافة مستخدم جديد
                    </button>
                </div>
            </div>

            <?php if ($success_message): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle me-2"></i>
                    <?php echo $success_message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?php if ($error_message): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <?php echo $error_message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <!-- قائمة المستخدمين -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-list me-2"></i>
                        قائمة المستخدمين (<?php echo count($users); ?>)
                    </h6>
                </div>
                <div class="card-body">
                    <?php if (empty($users)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-users-cog fa-3x text-muted mb-3"></i>
                            <h5>لا توجد مستخدمين</h5>
                            <p class="text-muted">ابدأ بإضافة أول مستخدم في النظام</p>
                            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#userModal" onclick="openAddModal()">
                                <i class="fas fa-plus me-1"></i>
                                إضافة مستخدم جديد
                            </button>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-bordered data-table" id="usersTable" width="100%" cellspacing="0">
                                <thead>
                                    <tr>
                                        <th>اسم المستخدم</th>
                                        <th>البريد الإلكتروني</th>
                                        <th>الموظف المرتبط</th>
                                        <th>الصلاحية</th>
                                        <th>الحالة</th>
                                        <th>آخر تسجيل دخول</th>
                                        <th>تاريخ الإنشاء</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($users as $user): ?>
                                    <tr>
                                        <td>
                                            <strong><?php echo htmlspecialchars($user['username']); ?></strong>
                                            <?php if ($user['id'] == $_SESSION['user_id']): ?>
                                                <span class="badge bg-info ms-1">أنت</span>
                                            <?php endif; ?>
                                        </td>
                                        <td><?php echo htmlspecialchars($user['email']); ?></td>
                                        <td>
                                            <?php if ($user['employee_id']): ?>
                                                <div>
                                                    <strong><?php echo htmlspecialchars($user['first_name'] . ' ' . $user['last_name']); ?></strong>
                                                    <br><small class="text-muted">
                                                        رقم: <?php echo htmlspecialchars($user['employee_number']); ?>
                                                        <?php if ($user['department_name']): ?>
                                                            | <?php echo htmlspecialchars($user['department_name']); ?>
                                                        <?php endif; ?>
                                                    </small>
                                                </div>
                                            <?php else: ?>
                                                <span class="text-muted">غير مرتبط</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php
                                            $role_classes = [
                                                'admin' => 'danger',
                                                'hr' => 'warning',
                                                'manager' => 'info',
                                                'employee' => 'success'
                                            ];
                                            $role_text = [
                                                'admin' => 'مدير النظام',
                                                'hr' => 'موارد بشرية',
                                                'manager' => 'مدير',
                                                'employee' => 'موظف'
                                            ];
                                            $role_class = $role_classes[$user['role']] ?? 'secondary';
                                            ?>
                                            <span class="badge bg-<?php echo $role_class; ?>">
                                                <?php echo $role_text[$user['role']] ?? $user['role']; ?>
                                            </span>
                                        </td>
                                        <td>
                                            <?php
                                            $status_classes = [
                                                'active' => 'success',
                                                'inactive' => 'secondary',
                                                'suspended' => 'danger'
                                            ];
                                            $status_text = [
                                                'active' => 'نشط',
                                                'inactive' => 'غير نشط',
                                                'suspended' => 'موقوف'
                                            ];
                                            $status_class = $status_classes[$user['status']] ?? 'secondary';
                                            ?>
                                            <span class="badge bg-<?php echo $status_class; ?>">
                                                <?php echo $status_text[$user['status']] ?? $user['status']; ?>
                                            </span>
                                        </td>
                                        <td>
                                            <?php if ($user['last_login']): ?>
                                                <small><?php echo formatDateTime($user['last_login']); ?></small>
                                            <?php else: ?>
                                                <span class="text-muted">لم يسجل دخول</span>
                                            <?php endif; ?>
                                        </td>
                                        <td><?php echo formatDate($user['created_at']); ?></td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <button type="button" class="btn btn-sm btn-outline-primary" 
                                                        onclick="openEditModal(<?php echo htmlspecialchars(json_encode($user)); ?>)">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <?php if ($user['id'] != $_SESSION['user_id']): ?>
                                                <button type="button" class="btn btn-sm btn-outline-danger" 
                                                        onclick="confirmDelete(<?php echo $user['id']; ?>, '<?php echo htmlspecialchars($user['username']); ?>')">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                                <?php else: ?>
                                                <button type="button" class="btn btn-sm btn-outline-secondary" disabled 
                                                        title="لا يمكن حذف حسابك الخاص">
                                                    <i class="fas fa-lock"></i>
                                                </button>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </main>
    </div>
</div>

<!-- Modal إضافة/تعديل مستخدم -->
<div class="modal fade" id="userModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="userModalTitle">إضافة مستخدم جديد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" id="userForm" class="needs-validation" novalidate>
                <div class="modal-body">
                    <input type="hidden" name="action" id="modal_action" value="add">
                    <input type="hidden" name="user_id" id="modal_user_id">
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="modal_username" class="form-label">اسم المستخدم <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="modal_username" name="username" required>
                            <div class="invalid-feedback">يرجى إدخال اسم المستخدم</div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="modal_email" class="form-label">البريد الإلكتروني <span class="text-danger">*</span></label>
                            <input type="email" class="form-control" id="modal_email" name="email" required>
                            <div class="invalid-feedback">يرجى إدخال بريد إلكتروني صحيح</div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="modal_password" class="form-label">كلمة المرور <span class="text-danger" id="password_required">*</span></label>
                            <input type="password" class="form-control" id="modal_password" name="password">
                            <div class="invalid-feedback">يرجى إدخال كلمة المرور (6 أحرف على الأقل)</div>
                            <small class="form-text text-muted" id="password_help">اتركها فارغة للاحتفاظ بكلمة المرور الحالية</small>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="modal_confirm_password" class="form-label">تأكيد كلمة المرور <span class="text-danger" id="confirm_password_required">*</span></label>
                            <input type="password" class="form-control" id="modal_confirm_password" name="confirm_password">
                            <div class="invalid-feedback">كلمة المرور وتأكيدها غير متطابقتين</div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="modal_role" class="form-label">الصلاحية</label>
                            <select class="form-select" id="modal_role" name="role">
                                <option value="employee">موظف</option>
                                <option value="manager">مدير</option>
                                <option value="hr">موارد بشرية</option>
                                <option value="admin">مدير النظام</option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="modal_status" class="form-label">الحالة</label>
                            <select class="form-select" id="modal_status" name="status">
                                <option value="active">نشط</option>
                                <option value="inactive">غير نشط</option>
                                <option value="suspended">موقوف</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="modal_employee_id" class="form-label">ربط بموظف</label>
                        <select class="form-select" id="modal_employee_id" name="employee_id">
                            <option value="">بدون ربط</option>
                            <?php foreach ($available_employees as $emp): ?>
                                <option value="<?php echo $emp['id']; ?>">
                                    <?php echo htmlspecialchars($emp['first_name'] . ' ' . $emp['last_name']); ?>
                                    (<?php echo htmlspecialchars($emp['employee_number']); ?>)
                                    <?php if ($emp['department_name']): ?>
                                        - <?php echo htmlspecialchars($emp['department_name']); ?>
                                    <?php endif; ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                        <small class="form-text text-muted">ربط المستخدم بملف موظف موجود</small>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary" id="modal_submit_btn">
                        <i class="fas fa-save me-1"></i>
                        حفظ
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// فتح modal للإضافة
function openAddModal() {
    document.getElementById('userModalTitle').textContent = 'إضافة مستخدم جديد';
    document.getElementById('modal_action').value = 'add';
    document.getElementById('modal_submit_btn').innerHTML = '<i class="fas fa-save me-1"></i>إضافة';
    
    // إظهار متطلبات كلمة المرور
    document.getElementById('password_required').style.display = 'inline';
    document.getElementById('confirm_password_required').style.display = 'inline';
    document.getElementById('password_help').style.display = 'none';
    document.getElementById('modal_password').required = true;
    document.getElementById('modal_confirm_password').required = true;
    
    // إعادة تعيين النموذج
    document.getElementById('userForm').reset();
    document.getElementById('userForm').classList.remove('was-validated');
}

// فتح modal للتعديل
function openEditModal(user) {
    document.getElementById('userModalTitle').textContent = 'تعديل المستخدم';
    document.getElementById('modal_action').value = 'edit';
    document.getElementById('modal_submit_btn').innerHTML = '<i class="fas fa-save me-1"></i>تحديث';
    
    // إخفاء متطلبات كلمة المرور
    document.getElementById('password_required').style.display = 'none';
    document.getElementById('confirm_password_required').style.display = 'none';
    document.getElementById('password_help').style.display = 'block';
    document.getElementById('modal_password').required = false;
    document.getElementById('modal_confirm_password').required = false;
    
    // ملء البيانات
    document.getElementById('modal_user_id').value = user.id;
    document.getElementById('modal_username').value = user.username;
    document.getElementById('modal_email').value = user.email;
    document.getElementById('modal_role').value = user.role;
    document.getElementById('modal_status').value = user.status;
    document.getElementById('modal_employee_id').value = user.employee_id || '';
    
    // إضافة الموظف الحالي إلى القائمة إذا كان مرتبطاً
    if (user.employee_id && user.first_name) {
        const employeeSelect = document.getElementById('modal_employee_id');
        const currentOption = document.createElement('option');
        currentOption.value = user.employee_id;
        currentOption.textContent = `${user.first_name} ${user.last_name} (${user.employee_number})`;
        currentOption.selected = true;
        employeeSelect.insertBefore(currentOption, employeeSelect.children[1]);
    }
    
    // إزالة validation classes
    document.getElementById('userForm').classList.remove('was-validated');
    
    // عرض المودال
    new bootstrap.Modal(document.getElementById('userModal')).show();
}

// تأكيد الحذف
function confirmDelete(userId, username) {
    Swal.fire({
        title: 'تأكيد الحذف',
        text: `هل تريد حذف المستخدم: ${username}؟`,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#6c757d',
        confirmButtonText: 'نعم، احذف',
        cancelButtonText: 'إلغاء'
    }).then((result) => {
        if (result.isConfirmed) {
            window.location.href = `users.php?delete=${userId}`;
        }
    });
}

// التحقق من تطابق كلمة المرور
document.getElementById('modal_confirm_password').addEventListener('input', function() {
    const password = document.getElementById('modal_password').value;
    const confirmPassword = this.value;
    
    if (password !== confirmPassword) {
        this.setCustomValidity('كلمة المرور وتأكيدها غير متطابقتين');
    } else {
        this.setCustomValidity('');
    }
});

// تفعيل التحقق من صحة النموذج
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();

// تهيئة DataTable
$(document).ready(function() {
    $('#usersTable').DataTable({
        language: {
            url: 'https://cdn.datatables.net/plug-ins/1.13.6/i18n/ar.json'
        },
        responsive: true,
        pageLength: 25,
        order: [[6, 'desc']],
        columnDefs: [
            { orderable: false, targets: [7] } // عمود الإجراءات غير قابل للترتيب
        ]
    });
});
</script>

<?php include '../includes/footer.php'; ?>
