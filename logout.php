<?php
session_start();

// تسجيل نشاط تسجيل الخروج
if (isset($_SESSION['user_id'])) {
    require_once 'config/database.php';
    
    try {
        $pdo = getConnection();
        $stmt = $pdo->prepare("INSERT INTO activity_log (user_id, action, ip_address, created_at) VALUES (?, 'تسجيل خروج', ?, NOW())");
        $stmt->execute([$_SESSION['user_id'], $_SERVER['REMOTE_ADDR']]);
    } catch (PDOException $e) {
        // تجاهل الأخطاء في تسجيل النشاط
    }
}

// إنهاء الجلسة
session_destroy();

// حذف ملفات تعريف الارتباط
if (isset($_COOKIE[session_name()])) {
    setcookie(session_name(), '', time()-3600, '/');
}

// إعادة التوجيه لصفحة تسجيل الدخول
header('Location: login.php?message=logout_success');
exit();
?>
