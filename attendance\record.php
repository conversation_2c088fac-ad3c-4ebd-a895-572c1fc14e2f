<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit();
}

$page_title = 'تسجيل الحضور والانصراف';
$success_message = '';
$error_message = '';

// الحصول على قائمة الموظفين النشطين
try {
    $pdo = getConnection();
    $stmt = $pdo->query("
        SELECT id, employee_number, first_name, last_name, department_id
        FROM employees 
        WHERE status = 'active' 
        ORDER BY first_name, last_name
    ");
    $employees = $stmt->fetchAll();
} catch (PDOException $e) {
    $employees = [];
}

// معالجة تسجيل الحضور/الانصراف
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    try {
        $employee_id = (int)$_POST['employee_id'];
        $action = $_POST['action']; // check_in أو check_out
        $current_time = date('H:i:s');
        $current_date = date('Y-m-d');
        
        // التحقق من وجود سجل لهذا اليوم
        $check_stmt = $pdo->prepare("
            SELECT * FROM attendance 
            WHERE employee_id = ? AND date = ?
        ");
        $check_stmt->execute([$employee_id, $current_date]);
        $existing_record = $check_stmt->fetch();
        
        if ($action == 'check_in') {
            if ($existing_record && $existing_record['check_in']) {
                throw new Exception('تم تسجيل الحضور مسبقاً لهذا اليوم');
            }
            
            // حساب التأخير
            $work_start_time = '08:00:00'; // يمكن جعلها قابلة للتخصيص
            $late_minutes = 0;
            
            if ($current_time > $work_start_time) {
                $start = new DateTime($work_start_time);
                $current = new DateTime($current_time);
                $diff = $current->diff($start);
                $late_minutes = ($diff->h * 60) + $diff->i;
            }
            
            if ($existing_record) {
                // تحديث السجل الموجود
                $update_stmt = $pdo->prepare("
                    UPDATE attendance 
                    SET check_in = ?, late_minutes = ?, status = 'حاضر', updated_at = NOW()
                    WHERE id = ?
                ");
                $update_stmt->execute([$current_time, $late_minutes, $existing_record['id']]);
            } else {
                // إنشاء سجل جديد
                $insert_stmt = $pdo->prepare("
                    INSERT INTO attendance (employee_id, date, check_in, late_minutes, status, created_at)
                    VALUES (?, ?, ?, ?, 'حاضر', NOW())
                ");
                $insert_stmt->execute([$employee_id, $current_date, $current_time, $late_minutes]);
            }
            
            $success_message = 'تم تسجيل الحضور بنجاح';
            if ($late_minutes > 0) {
                $success_message .= " (تأخير: $late_minutes دقيقة)";
            }
            
        } elseif ($action == 'check_out') {
            if (!$existing_record || !$existing_record['check_in']) {
                throw new Exception('يجب تسجيل الحضور أولاً');
            }
            
            if ($existing_record['check_out']) {
                throw new Exception('تم تسجيل الانصراف مسبقاً لهذا اليوم');
            }
            
            // حساب ساعات العمل
            $check_in_time = new DateTime($existing_record['check_in']);
            $check_out_time = new DateTime($current_time);
            $work_duration = $check_out_time->diff($check_in_time);
            $total_hours = $work_duration->h + ($work_duration->i / 60);
            
            // حساب الانصراف المبكر
            $work_end_time = '17:00:00'; // يمكن جعلها قابلة للتخصيص
            $early_leave_minutes = 0;
            
            if ($current_time < $work_end_time) {
                $end = new DateTime($work_end_time);
                $current = new DateTime($current_time);
                $diff = $end->diff($current);
                $early_leave_minutes = ($diff->h * 60) + $diff->i;
            }
            
            // حساب الساعات الإضافية
            $standard_hours = 8; // ساعات العمل القياسية
            $overtime_hours = max(0, $total_hours - $standard_hours);
            
            $update_stmt = $pdo->prepare("
                UPDATE attendance 
                SET check_out = ?, total_hours = ?, overtime_hours = ?, 
                    early_leave_minutes = ?, updated_at = NOW()
                WHERE id = ?
            ");
            $update_stmt->execute([
                $current_time, 
                $total_hours, 
                $overtime_hours, 
                $early_leave_minutes, 
                $existing_record['id']
            ]);
            
            $success_message = 'تم تسجيل الانصراف بنجاح';
            if ($early_leave_minutes > 0) {
                $success_message .= " (انصراف مبكر: $early_leave_minutes دقيقة)";
            }
            if ($overtime_hours > 0) {
                $success_message .= " (ساعات إضافية: " . number_format($overtime_hours, 2) . ")";
            }
        }
        
        // تسجيل النشاط
        $employee_name = '';
        foreach ($employees as $emp) {
            if ($emp['id'] == $employee_id) {
                $employee_name = $emp['first_name'] . ' ' . $emp['last_name'];
                break;
            }
        }
        
        $action_text = $action == 'check_in' ? 'تسجيل حضور' : 'تسجيل انصراف';
        logActivity($_SESSION['user_id'], $action_text, "الموظف: $employee_name");
        
    } catch (Exception $e) {
        $error_message = $e->getMessage();
    }
}

// الحصول على سجلات اليوم
try {
    $today_stmt = $pdo->prepare("
        SELECT 
            a.*,
            e.employee_number,
            e.first_name,
            e.last_name
        FROM attendance a
        JOIN employees e ON a.employee_id = e.id
        WHERE a.date = CURDATE()
        ORDER BY a.check_in DESC
    ");
    $today_stmt->execute();
    $today_records = $today_stmt->fetchAll();
} catch (PDOException $e) {
    $today_records = [];
}

include '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
            <div class="position-sticky pt-3">
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link" href="../index.php">
                            <i class="fas fa-tachometer-alt me-2"></i>
                            لوحة التحكم
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../employees/list.php">
                            <i class="fas fa-users me-2"></i>
                            قائمة الموظفين
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="record.php">
                            <i class="fas fa-fingerprint me-2"></i>
                            تسجيل الحضور
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="reports.php">
                            <i class="fas fa-chart-bar me-2"></i>
                            تقارير الحضور
                        </a>
                    </li>
                </ul>
            </div>
        </nav>
        
        <!-- Main Content -->
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">
                    <i class="fas fa-fingerprint me-2"></i>
                    تسجيل الحضور والانصراف
                </h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <span class="btn btn-outline-info" id="currentDateTime">
                            <i class="fas fa-clock me-1"></i>
                            <span id="currentTime"></span>
                        </span>
                    </div>
                </div>
            </div>

            <?php if ($success_message): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle me-2"></i>
                    <?php echo $success_message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?php if ($error_message): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <?php echo $error_message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <!-- نموذج تسجيل الحضور -->
            <div class="row mb-4">
                <div class="col-lg-6">
                    <div class="card shadow">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">
                                <i class="fas fa-user-clock me-2"></i>
                                تسجيل حضور/انصراف
                            </h6>
                        </div>
                        <div class="card-body">
                            <form method="POST" id="attendanceForm">
                                <div class="mb-3">
                                    <label for="employee_id" class="form-label">اختر الموظف</label>
                                    <select class="form-select" id="employee_id" name="employee_id" required>
                                        <option value="">-- اختر الموظف --</option>
                                        <?php foreach ($employees as $employee): ?>
                                            <option value="<?php echo $employee['id']; ?>">
                                                <?php echo htmlspecialchars($employee['employee_number'] . ' - ' . $employee['first_name'] . ' ' . $employee['last_name']); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                
                                <div class="row">
                                    <div class="col-6">
                                        <button type="submit" name="action" value="check_in" class="btn btn-success btn-lg w-100">
                                            <i class="fas fa-sign-in-alt me-2"></i>
                                            تسجيل حضور
                                        </button>
                                    </div>
                                    <div class="col-6">
                                        <button type="submit" name="action" value="check_out" class="btn btn-danger btn-lg w-100">
                                            <i class="fas fa-sign-out-alt me-2"></i>
                                            تسجيل انصراف
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-6">
                    <!-- إحصائيات سريعة -->
                    <div class="row">
                        <div class="col-12 mb-3">
                            <div class="card stats-card success border-0 shadow-sm">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col mr-2">
                                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                                الحاضرون اليوم
                                            </div>
                                            <div class="h5 mb-0 font-weight-bold text-gray-800 counter">
                                                <?php echo count(array_filter($today_records, function($r) { return $r['check_in']; })); ?>
                                            </div>
                                        </div>
                                        <div class="col-auto">
                                            <i class="fas fa-user-check fa-2x text-gray-300"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-12 mb-3">
                            <div class="card stats-card warning border-0 shadow-sm">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col mr-2">
                                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                                المتأخرون اليوم
                                            </div>
                                            <div class="h5 mb-0 font-weight-bold text-gray-800 counter">
                                                <?php echo count(array_filter($today_records, function($r) { return $r['late_minutes'] > 0; })); ?>
                                            </div>
                                        </div>
                                        <div class="col-auto">
                                            <i class="fas fa-clock fa-2x text-gray-300"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-12">
                            <div class="card stats-card info border-0 shadow-sm">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col mr-2">
                                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                                لم ينصرفوا بعد
                                            </div>
                                            <div class="h5 mb-0 font-weight-bold text-gray-800 counter">
                                                <?php echo count(array_filter($today_records, function($r) { return $r['check_in'] && !$r['check_out']; })); ?>
                                            </div>
                                        </div>
                                        <div class="col-auto">
                                            <i class="fas fa-user-clock fa-2x text-gray-300"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- سجلات اليوم -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-calendar-day me-2"></i>
                        سجلات اليوم (<?php echo date('Y-m-d'); ?>)
                    </h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered" id="todayRecordsTable" width="100%" cellspacing="0">
                            <thead>
                                <tr>
                                    <th>الرقم الوظيفي</th>
                                    <th>اسم الموظف</th>
                                    <th>وقت الحضور</th>
                                    <th>وقت الانصراف</th>
                                    <th>ساعات العمل</th>
                                    <th>التأخير</th>
                                    <th>الحالة</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($today_records as $record): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($record['employee_number']); ?></td>
                                    <td><?php echo htmlspecialchars($record['first_name'] . ' ' . $record['last_name']); ?></td>
                                    <td>
                                        <?php if ($record['check_in']): ?>
                                            <span class="badge bg-success">
                                                <?php echo formatTime($record['check_in']); ?>
                                            </span>
                                        <?php else: ?>
                                            <span class="text-muted">لم يحضر</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if ($record['check_out']): ?>
                                            <span class="badge bg-danger">
                                                <?php echo formatTime($record['check_out']); ?>
                                            </span>
                                        <?php elseif ($record['check_in']): ?>
                                            <span class="text-warning">لم ينصرف</span>
                                        <?php else: ?>
                                            <span class="text-muted">-</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if ($record['total_hours']): ?>
                                            <?php echo number_format($record['total_hours'], 2); ?> ساعة
                                        <?php else: ?>
                                            <span class="text-muted">-</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if ($record['late_minutes'] > 0): ?>
                                            <span class="badge bg-warning">
                                                <?php echo $record['late_minutes']; ?> دقيقة
                                            </span>
                                        <?php else: ?>
                                            <span class="text-success">في الوقت</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php
                                        $status_classes = [
                                            'حاضر' => 'success',
                                            'غائب' => 'danger',
                                            'إجازة' => 'info',
                                            'مرض' => 'warning'
                                        ];
                                        $status_class = $status_classes[$record['status']] ?? 'secondary';
                                        ?>
                                        <span class="badge bg-<?php echo $status_class; ?>">
                                            <?php echo htmlspecialchars($record['status']); ?>
                                        </span>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<script>
// تحديث الوقت الحالي
function updateCurrentTime() {
    const now = new Date();
    const timeString = now.toLocaleString('ar-SA', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });
    document.getElementById('currentTime').textContent = timeString;
}

// تحديث الوقت كل ثانية
setInterval(updateCurrentTime, 1000);
updateCurrentTime();

// تهيئة DataTable
$(document).ready(function() {
    $('#todayRecordsTable').DataTable({
        language: {
            url: 'https://cdn.datatables.net/plug-ins/1.13.6/i18n/ar.json'
        },
        responsive: true,
        pageLength: 25,
        order: [[2, 'desc']]
    });
});

// تأكيد تسجيل الحضور/الانصراف
$('#attendanceForm').on('submit', function(e) {
    const employeeSelect = $('#employee_id');
    const action = e.originalEvent.submitter.value;
    
    if (!employeeSelect.val()) {
        e.preventDefault();
        showError('يرجى اختيار الموظف أولاً');
        return;
    }
    
    const employeeName = employeeSelect.find('option:selected').text();
    const actionText = action === 'check_in' ? 'تسجيل الحضور' : 'تسجيل الانصراف';
    
    e.preventDefault();
    
    Swal.fire({
        title: 'تأكيد العملية',
        text: `هل تريد ${actionText} للموظف: ${employeeName}؟`,
        icon: 'question',
        showCancelButton: true,
        confirmButtonColor: action === 'check_in' ? '#28a745' : '#dc3545',
        cancelButtonColor: '#6c757d',
        confirmButtonText: 'نعم، تأكيد',
        cancelButtonText: 'إلغاء'
    }).then((result) => {
        if (result.isConfirmed) {
            // إرسال النموذج
            const form = document.createElement('form');
            form.method = 'POST';
            form.innerHTML = `
                <input type="hidden" name="employee_id" value="${employeeSelect.val()}">
                <input type="hidden" name="action" value="${action}">
            `;
            document.body.appendChild(form);
            form.submit();
        }
    });
});
</script>

<?php include '../includes/footer.php'; ?>
