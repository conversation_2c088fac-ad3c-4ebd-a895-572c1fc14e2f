<?php
session_start();
require_once '../config/database.php';

header('Content-Type: application/json');

if (!isset($_SESSION['user_id'])) {
    echo json_encode(['count' => 0]);
    exit();
}

try {
    $pdo = getConnection();
    
    // الحصول على عدد الإشعارات غير المقروءة
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM notifications WHERE user_id = ? AND is_read = 0");
    $stmt->execute([$_SESSION['user_id']]);
    $count = $stmt->fetchColumn();
    
    echo json_encode(['count' => (int)$count]);
    
} catch (PDOException $e) {
    echo json_encode(['count' => 0, 'error' => 'Database error']);
}
?>
