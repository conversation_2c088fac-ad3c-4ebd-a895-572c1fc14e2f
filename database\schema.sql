-- إنشاء قاعدة البيانات
CREATE DATABASE IF NOT EXISTS employee_management CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE employee_management;

-- جدول المستخدمين
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHA<PERSON>(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    role ENUM('admin', 'hr', 'manager', 'employee') DEFAULT 'employee',
    employee_id INT UNIQUE,
    status ENUM('active', 'inactive', 'suspended') DEFAULT 'active',
    last_login TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- جدول الأقسام
CREATE TABLE departments (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    manager_name VARCHA<PERSON>(100),
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- جدول المسميات الوظيفية
CREATE TABLE positions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(100) NOT NULL,
    department_id INT,
    description TEXT,
    min_salary DECIMAL(10,2),
    max_salary DECIMAL(10,2),
    requirements TEXT,
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (department_id) REFERENCES departments(id) ON DELETE SET NULL
);

-- جدول الموظفين
CREATE TABLE employees (
    id INT PRIMARY KEY AUTO_INCREMENT,
    employee_number VARCHAR(20) UNIQUE NOT NULL,
    user_id INT UNIQUE,
    
    -- البيانات الشخصية
    first_name VARCHAR(50) NOT NULL,
    last_name VARCHAR(50) NOT NULL,
    national_id VARCHAR(20) UNIQUE NOT NULL,
    birth_date DATE NOT NULL,
    gender ENUM('male', 'female') NOT NULL,
    city VARCHAR(50),
    address TEXT,
    phone VARCHAR(20),
    email VARCHAR(100),
    identity_number VARCHAR(20),
    
    -- البيانات الوظيفية
    department_id INT,
    position_id INT,
    hire_date DATE NOT NULL,
    contract_start_date DATE,
    contract_end_date DATE,
    qualification ENUM('ثانوية', 'دبلوم متوسط', 'دبلوم عالي', 'بكالوريوس', 'ليسانس', 'ماجستير', 'دكتوراة'),
    salary DECIMAL(10,2),
    status ENUM('active', 'inactive', 'terminated', 'resigned') DEFAULT 'active',
    
    -- معلومات إضافية
    emergency_contact_name VARCHAR(100),
    emergency_contact_phone VARCHAR(20),
    notes TEXT,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (department_id) REFERENCES departments(id) ON DELETE SET NULL,
    FOREIGN KEY (position_id) REFERENCES positions(id) ON DELETE SET NULL
);

-- جدول مستندات الموظفين
CREATE TABLE employee_documents (
    id INT PRIMARY KEY AUTO_INCREMENT,
    employee_id INT NOT NULL,
    document_type ENUM('هوية', 'عقد', 'شهادة', 'سيرة ذاتية', 'صورة شخصية', 'أخرى') NOT NULL,
    document_name VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size INT,
    upload_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    uploaded_by INT,
    
    FOREIGN KEY (employee_id) REFERENCES employees(id) ON DELETE CASCADE,
    FOREIGN KEY (uploaded_by) REFERENCES users(id) ON DELETE SET NULL
);

-- جدول سجل تغييرات الحالة الوظيفية
CREATE TABLE employee_status_history (
    id INT PRIMARY KEY AUTO_INCREMENT,
    employee_id INT NOT NULL,
    change_type ENUM('ترقية', 'نقل', 'زيادة راتب', 'تغيير قسم', 'تغيير منصب', 'استقالة', 'إنهاء خدمة', 'أخرى') NOT NULL,
    old_value VARCHAR(255),
    new_value VARCHAR(255),
    effective_date DATE NOT NULL,
    reason TEXT,
    notes TEXT,
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (employee_id) REFERENCES employees(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
);

-- جدول الحضور والانصراف
CREATE TABLE attendance (
    id INT PRIMARY KEY AUTO_INCREMENT,
    employee_id INT NOT NULL,
    date DATE NOT NULL,
    check_in TIME,
    check_out TIME,
    break_start TIME,
    break_end TIME,
    total_hours DECIMAL(4,2),
    overtime_hours DECIMAL(4,2) DEFAULT 0,
    late_minutes INT DEFAULT 0,
    early_leave_minutes INT DEFAULT 0,
    status ENUM('حاضر', 'غائب', 'إجازة', 'مرض', 'مأمورية') DEFAULT 'حاضر',
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (employee_id) REFERENCES employees(id) ON DELETE CASCADE,
    UNIQUE KEY unique_employee_date (employee_id, date)
);

-- جدول أنواع الإجازات
CREATE TABLE leave_types (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(50) NOT NULL,
    description TEXT,
    max_days_per_year INT,
    max_consecutive_days INT,
    requires_approval BOOLEAN DEFAULT TRUE,
    is_paid BOOLEAN DEFAULT TRUE,
    carry_forward BOOLEAN DEFAULT FALSE,
    gender_specific ENUM('male', 'female') NULL,
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- جدول أرصدة الإجازات
CREATE TABLE leave_balances (
    id INT PRIMARY KEY AUTO_INCREMENT,
    employee_id INT NOT NULL,
    leave_type_id INT NOT NULL,
    year YEAR NOT NULL,
    total_days INT NOT NULL,
    used_days INT DEFAULT 0,
    remaining_days INT NOT NULL,
    carried_forward INT DEFAULT 0,
    
    FOREIGN KEY (employee_id) REFERENCES employees(id) ON DELETE CASCADE,
    FOREIGN KEY (leave_type_id) REFERENCES leave_types(id) ON DELETE CASCADE,
    UNIQUE KEY unique_employee_leave_year (employee_id, leave_type_id, year)
);

-- جدول طلبات الإجازات
CREATE TABLE leave_requests (
    id INT PRIMARY KEY AUTO_INCREMENT,
    employee_id INT NOT NULL,
    leave_type_id INT NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    days_requested INT NOT NULL,
    reason TEXT,
    status ENUM('pending', 'approved', 'rejected', 'cancelled') DEFAULT 'pending',
    
    -- نظام الموافقة متعدد المستويات
    requested_by INT NOT NULL,
    approved_by_manager INT,
    approved_by_hr INT,
    manager_approval_date TIMESTAMP NULL,
    hr_approval_date TIMESTAMP NULL,
    manager_notes TEXT,
    hr_notes TEXT,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (employee_id) REFERENCES employees(id) ON DELETE CASCADE,
    FOREIGN KEY (leave_type_id) REFERENCES leave_types(id) ON DELETE CASCADE,
    FOREIGN KEY (requested_by) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (approved_by_manager) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (approved_by_hr) REFERENCES users(id) ON DELETE SET NULL
);

-- جدول الإشعارات
CREATE TABLE notifications (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT,
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    type ENUM('info', 'warning', 'success', 'error') DEFAULT 'info',
    is_read BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- جدول سجل الأنشطة
CREATE TABLE activity_log (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT,
    action VARCHAR(255) NOT NULL,
    details TEXT,
    ip_address VARCHAR(45),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
);

-- جدول إعدادات الشركة
CREATE TABLE company_settings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    company_name VARCHAR(255) NOT NULL,
    company_name_en VARCHAR(255),
    address TEXT,
    phone VARCHAR(50),
    email VARCHAR(100),
    website VARCHAR(255),
    tax_number VARCHAR(50),
    commercial_register VARCHAR(50),
    logo VARCHAR(500),
    work_start_time TIME DEFAULT '08:00:00',
    work_end_time TIME DEFAULT '17:00:00',
    weekend_days VARCHAR(100) DEFAULT 'friday,saturday',
    currency VARCHAR(10) DEFAULT 'SAR',
    timezone VARCHAR(50) DEFAULT 'Asia/Riyadh',
    language VARCHAR(10) DEFAULT 'ar',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- إدراج البيانات الأساسية

-- إدراج أنواع الإجازات الافتراضية
INSERT INTO leave_types (name, description, max_days_per_year, max_consecutive_days, is_paid, carry_forward, gender_specific) VALUES
('إجازة سنوية', 'الإجازة السنوية العادية', 30, 15, TRUE, TRUE, NULL),
('إجازة مرضية', 'إجازة للحالات المرضية', 15, 7, TRUE, FALSE, NULL),
('إجازة اضطرارية', 'إجازة للظروف الطارئة', 5, 3, FALSE, FALSE, NULL),
('إجازة أمومة', 'إجازة الأمومة للموظفات', 90, 90, TRUE, FALSE, 'female'),
('إجازة أبوة', 'إجازة الأبوة للموظفين', 3, 3, TRUE, FALSE, 'male'),
('إجازة حج', 'إجازة لأداء فريضة الحج', 15, 15, TRUE, FALSE, NULL);

-- إدراج الأقسام الافتراضية
INSERT INTO departments (name, description) VALUES
('الموارد البشرية', 'قسم إدارة الموارد البشرية والشؤون الإدارية'),
('المالية والمحاسبة', 'قسم الشؤون المالية والمحاسبية'),
('تقنية المعلومات', 'قسم تقنية المعلومات والدعم التقني'),
('التسويق والمبيعات', 'قسم التسويق والمبيعات'),
('العمليات', 'قسم العمليات والإنتاج'),
('خدمة العملاء', 'قسم خدمة العملاء والدعم');

-- إدراج إعدادات الشركة الافتراضية
INSERT INTO company_settings (
    company_name, company_name_en, address, phone, email, website,
    work_start_time, work_end_time, weekend_days, currency, timezone, language
) VALUES (
    'شركة المثال للتجارة', 'Example Trading Company',
    'الرياض، المملكة العربية السعودية', '+966-11-1234567',
    '<EMAIL>', 'www.example.com',
    '08:00:00', '17:00:00', 'friday,saturday', 'SAR', 'Asia/Riyadh', 'ar'
);

-- إنشاء المستخدم الافتراضي (admin)
INSERT INTO users (username, email, password, role) VALUES
('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'admin');

-- إضافة المفاتيح الخارجية للمستخدمين
ALTER TABLE users ADD FOREIGN KEY (employee_id) REFERENCES employees(id) ON DELETE SET NULL;

-- إنشاء فهارس لتحسين الأداء
CREATE INDEX idx_employees_department ON employees(department_id);
CREATE INDEX idx_employees_status ON employees(status);
CREATE INDEX idx_attendance_employee_date ON attendance(employee_id, date);
CREATE INDEX idx_leave_requests_employee ON leave_requests(employee_id);
CREATE INDEX idx_leave_requests_status ON leave_requests(status);
CREATE INDEX idx_notifications_user ON notifications(user_id);
CREATE INDEX idx_activity_log_user ON activity_log(user_id);
CREATE INDEX idx_activity_log_date ON activity_log(created_at);
